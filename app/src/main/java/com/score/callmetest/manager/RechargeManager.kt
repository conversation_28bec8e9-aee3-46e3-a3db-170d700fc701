package com.score.callmetest.manager

import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.os.Bundle
import android.text.SpannableString
import androidx.appcompat.content.res.AppCompatResources
import androidx.core.graphics.toColorInt
import androidx.fragment.app.FragmentActivity
import com.android.billingclient.api.BillingClient.BillingResponseCode
import com.android.billingclient.api.BillingFlowParams
import com.android.billingclient.api.Purchase
import com.score.callmetest.CallmeApplication
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.entity.RechargeSource
import com.score.callmetest.network.CreateRechargeRequest
import com.score.callmetest.network.CreateRechargeResponse
import com.score.callmetest.network.GooglePlayPaymentVerifyRequest
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.PayChannelItem
import com.score.callmetest.network.PurchaseEventType
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.ui.chat.ChatActivity
import com.score.callmetest.ui.web.WebViewActivity
import com.score.callmetest.ui.widget.BaseCustomDialog
import com.score.callmetest.ui.widget.PaymentMethodDialog
import com.score.callmetest.ui.widget.PaymentMethodItem
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.EventBus
import com.score.callmetest.util.HeaderUtils
import com.score.callmetest.util.LoadingUtils
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.ToastUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json
import timber.log.Timber
import java.util.concurrent.atomic.AtomicBoolean


/**
 * 充值流程管理器
 *
 * 这个单例对象负责处理完整的充值流程，包括：
 * 1. 根据用户来源（广告/非广告）决定是否显示支付方式选择弹窗
 * 2. 创建充值订单
 * 3. 处理Google Play支付和H5支付
 * 4. 订单验证和重试机制
 * 5. 支付成功/失败的UI反馈
 * 6. 未完成订单的自动补单机制
 *
 * 主要支持的支付方式：
 * - Google Play支付（默认）
 * - H5网页支付（第三方支付渠道）
 *
 * 充值流程：
 * startRecharge -> createRechargeOrder -> rechargeWithGooglePlay/H5支付 ->
 * verifyGooglePlayPaymentWithRetry -> handleRechargeSuccess/handleRechargeFailed
 */
object RechargeManager {

    const val DEFAULT_CURRENCY = "USD"

    /** 轮询间隔 */
    private const val POLL_INTERVAL = 5 * 60 * 1000L

    // 订单信息缓存---只缓存三方支付的信息，谷歌支付订单信息不缓存
    private var mCacheOrderInfoMap = mutableMapOf<String, CreateRechargeResponse>()

    private var mCacheSuccessOrder = mutableListOf<String>()
    private var mCacheFailedOrder = mutableListOf<String>()

    /** 是否登出 */
    private val mIsLogout = AtomicBoolean(true)

    /** 轮询相关变量 */
    private var mPollingJob: Job? = null
    private var mCurrentPollingInterval = POLL_INTERVAL
    private var mCurrentPollingTimes = -1 // -1表示无限轮询
    private var mExecutedPollingTimes = 0

    /** 轮询策略版本号，用于识别是否为最新的轮询策略 */
    private var mPollingStrategyVersion = 0L

    fun login(){
        mIsLogout.set(false)
        loadOrderInfoFromSp()
        // 开启轮询
        startPolling()
    }

    /**
     * 登出时需要清理一下
     */
    fun logout(){
        mIsLogout.set(true)
        mCacheOrderInfoMap.clear()
        // 取消轮询
        stopPolling()
    }

    fun cleanOrderInfo(){
        try {
            SharePreferenceUtil.remove(Constant.KEY_ORDER_INFOS)
        } catch (e: Exception) {
            // 静默处理
            Timber.tag("RechargeManager").e(e, "Failed to remove order info from SharedPreferences")
        }
    }

    private fun loadOrderInfoFromSp() {
        try {
            val orderInfoJson = SharePreferenceUtil.getString(Constant.KEY_ORDER_INFOS, null)
            if (orderInfoJson != null) {
                val orderMap = Json.decodeFromString<Map<String, CreateRechargeResponse>>(orderInfoJson)
                mCacheOrderInfoMap.clear()
                mCacheOrderInfoMap.putAll(orderMap)
            }
        } catch (e: Exception) {
            // 解析失败，静默处理
            Timber.tag("RechargeManager").e(e, "Failed to load order info from SharedPreferences")
        }
    }

    private fun addOrderInfoToSp(newItem: CreateRechargeResponse) {
        mCacheOrderInfoMap[newItem.orderNo!!] = newItem
        saveOrderInfoToSp()
    }

    private fun removeOrderInfoFromSp(orderNo: String) {
        val exist = mCacheOrderInfoMap.remove(orderNo) != null
        if (exist) {
            saveOrderInfoToSp()
        }
    }

    private fun saveOrderInfoToSp() {
        try {
            val jsonString = Json.encodeToString(mCacheOrderInfoMap)
            SharePreferenceUtil.putString(Constant.KEY_ORDER_INFOS, jsonString)
        } catch (e: Exception) {
            // 保存失败，静默处理
            Timber.tag("RechargeManager").e(e, "Failed to save order info from SharedPreferences")
        }
    }

    /**
     * 开始充值流程的入口方法
     *
     * 根据用户来源（通过Adjust判断是否来自广告）决定充值流程：
     * - 广告用户：显示支付方式选择弹窗，让用户选择支付渠道
     * - 非广告用户：直接使用默认支付方式（Google Play）
     *
     * @param activity 当前Activity，用于显示弹窗和启动支付页面
     * @param goodsCode 商品编码，用于标识要购买的商品
     * @param goodsName 商品名称，用于显示和日志记录
     * @param payChannel 支付渠道，默认为Google Play
     * @param invitationId 邀请ID，用于追踪推广来源
     * @param isSubscription 是否为订阅商品，影响支付流程和确认方式
     * @param entry 充值入口来源，用于数据统计和分析
     */
    fun startRecharge(
        activity: Activity,
        goodsCode: String,
        goodsName: String? = null,
        payChannel: String = PaymentMethodManager.PAY_CHANNEL_GP,
        invitationId: String? = null,
        isSubscription: Boolean = false,
        entry: RechargeSource = RechargeSource.SUBSCRIBE_DETAIL
    ) {
        // 通过Adjust判断当前应用是否来自广告来源
        val isAdUser = AdjustManager.isFromAdvertisementSource()

        // TODO: 当前为测试模式，强制显示支付方式选择弹窗
        // 正式环境应该使用 if (isAdUser) 判断
        if (true) {
//        if (isAdUser) {
            // 如果是广告来源，显示支付方式选择弹窗
            // 让用户在多种支付方式中选择，提高支付成功率
            Timber.tag("dsc--Recharge").d("检测到广告来源，显示支付方式选择弹窗")
            showPaymentMethodDialog(activity, goodsCode, goodsName) { selectedPayChannel ->
                // 用户选择支付方式后，开始充值流程
                startRechargeInternal(
                    activity = activity,
                    goodsCode = goodsCode,
                    goodsName = goodsName,
                    payChannel = selectedPayChannel,
                    invitationId = invitationId,
                    isSubscription = isSubscription,
                    entry = entry
                )
            }
        } else {
            // 如果不是广告来源，直接使用默认支付方式（Google Play）
            // 非广告用户通常对Google Play支付更熟悉，直接使用可以减少流程复杂度
            Timber.tag("dsc--Recharge").d("非广告来源，使用默认支付方式: ${PaymentMethodManager.PAY_CHANNEL_GP}")
            // showPaymentMethodDialog(activity, goodsCode, goodsName) { ... } // 已注释：非广告来源不显示支付方式选择
            startRechargeInternal(
                activity = activity,
                goodsCode = goodsCode,
                goodsName = goodsName,
                payChannel = payChannel,
                invitationId = invitationId,
                isSubscription = isSubscription,
                entry = entry
            )
        }
    }

    /**
     * 开始充值流程的内部实现方法
     *
     * 这个方法是实际执行充值逻辑的核心方法，主要流程：
     * 1. 显示加载动画
     * 2. 创建充值订单
     * 3. 根据支付渠道选择相应的支付方式
     * 4. 处理支付结果
     *
     * @param activity 当前Activity
     * @param goodsCode 商品编码
     * @param goodsName 商品名称
     * @param payChannel 支付渠道（Google Play或第三方）
     * @param invitationId 邀请ID
     * @param isSubscription 是否为订阅商品
     * @param entry 充值入口来源
     */
    private fun startRechargeInternal(
        activity: Activity,
        goodsCode: String,
        goodsName: String? = null,
        payChannel: String = PaymentMethodManager.PAY_CHANNEL_GP,
        invitationId: String? = null,
        isSubscription: Boolean = false,
        entry: RechargeSource = RechargeSource.SUBSCRIBE_DETAIL,
    ) {
        // 显示加载动画，提升用户体验
        LoadingUtils.showLoading(activity)

        // 创建充值订单
        createRechargeOrder(
            goodsCode = goodsCode,
            payChannel = payChannel,
            invitationId = invitationId,
            entry = entry,
            onSuccess = { data ->
                val orderNo = data.orderNo

                // 向Adjust报告订单事件，用于广告效果追踪
                AdjustManager.reportOrderEvent(
                    orderId = orderNo!!,
                    revenue = data.paidAmount!!,
                    currency = data.paidCurrency!!
                )

                // 记录购买事件日志，用于数据分析
                LogReportManager.reportPurchaseEvent(
                    orderId = orderNo,
                    event = PurchaseEventType.REVIEW_ORDER,
                    code = goodsCode,
                )

                // 保存用户选择的支付方式，用于下次充值时的默认选项
                PaymentMethodManager.saveLastUsedPaymentMethod(payChannel)

                // 根据支付渠道选择相应的支付方式
                if(payChannel == PaymentMethodManager.PAY_CHANNEL_GP){
                    // Google Play支付流程
                    rechargeWithGooglePlay(activity,goodsCode,goodsName,payChannel,invitationId,isSubscription,entry,data)
                    return@createRechargeOrder
                }

                // H5网页支付流程（第三方支付渠道）
                if(!data.requestUrl.isNullOrBlank()){
                    val h5Url = data.requestUrl
                    // 缓存订单信息，用于支付结果处理
                    addOrderInfoToSp(data)

                    // 判断是否需要跳转到外部浏览器
                    val isExternal = PaymentMethodManager.isNeedJumpExternalWeb(payChannel)
                    LoadingUtils.dismissLoading()
                    if(isExternal){
                        // 使用外部浏览器打开支付页面
                        ActivityUtils.openExternalWeb(activity,h5Url)
                    }else {
                        // 使用应用内WebView打开支付页面
                        ActivityUtils.startActivity(activity, WebViewActivity::class.java,
                            Bundle().apply {
                                putString("url", h5Url)
                                putString("title", activity.getString(R.string.pay_webview_title))
                                putDouble(WebViewActivity.PRICE, data.paidAmount)
                                putString(WebViewActivity.CURRENCY, data.paidCurrency)
                            }
                            ,false)
                    }
                    return@createRechargeOrder
                }

                // 其他情况默认使用Google Play支付
                rechargeWithGooglePlay(activity,goodsCode,goodsName,payChannel,invitationId,isSubscription,entry,data)

            },
            onFail = { code, msg ->
                // 订单创建失败，隐藏加载动画并显示错误提示
                LoadingUtils.dismissLoading()
                // 显示本地化的错误提示信息
                ToastUtils.showToast(CallmeApplication.context.getString(R.string.create_order_failed))
            }
        )
    }

    /**
     * 使用Google Play进行充值的具体实现
     *
     * 这个方法处理Google Play支付的完整流程：
     * 1. 查询商品信息
     * 2. 构建支付参数
     * 3. 启动支付页面
     * 4. 处理支付结果
     * 5. 验证订单
     * 6. 确认/消费购买
     *
     * @param activity 当前Activity
     * @param goodsCode 商品编码
     * @param goodsName 商品名称
     * @param payChannel 支付渠道
     * @param invitationId 邀请ID
     * @param isSubscription 是否为订阅商品
     * @param entry 充值入口来源
     * @param data 创建订单的响应数据
     */
    private fun rechargeWithGooglePlay(
        activity: Activity,
        goodsCode: String,
        goodsName: String? = null,
        payChannel: String,
        invitationId: String? = null,
        isSubscription: Boolean = false,
        entry: RechargeSource = RechargeSource.SUBSCRIBE_DETAIL,
        data: CreateRechargeResponse
    ){
        val orderNo = data.orderNo!!

        // 查询Google Play商品信息
        GooglePlayBillingManager.queryProducts(
            productIds = listOf(goodsCode),
            isSubscription = isSubscription,
            onSuccess = { productMap ->
                // 检查是否成功获取到商品信息
                if (productMap.isEmpty()) {
                    LogReportManager.reportPurchaseEvent(
                        orderId = orderNo,
                        event = PurchaseEventType.REVIEW_ORDER_RESPONSE,
                        code = goodsCode,
                        result = Constant.FAIL,
                        resultCode = BillingResponseCode.NETWORK_ERROR
                    )
                    LoadingUtils.dismissLoading()
                    return@queryProducts
                }

                // 记录查询商品成功的日志
                LogReportManager.reportPurchaseEvent(
                    orderId = orderNo,
                    event = PurchaseEventType.REVIEW_ORDER_RESPONSE,
                    code = goodsCode,
                )

                // 获取指定商品的详细信息
                val productDetails = productMap[goodsCode]
                if (productDetails == null) {
                    Timber.tag("dsc--Billing").e("未找到商品: $goodsCode")
                    LoadingUtils.dismissLoading()
                    return@queryProducts
                }

                // 获取offerToken（支付令牌）
                // 订阅商品和一次性购买商品的获取方式不同
                val offerToken = if (isSubscription) {
                    productDetails.subscriptionOfferDetails?.firstOrNull()?.offerToken
                } else {
                    productDetails.oneTimePurchaseOfferDetailsList?.firstOrNull()?.offerToken
                }

                // 验证offerToken的有效性
                if (offerToken.isNullOrEmpty()) {
                    Timber.tag("dsc--Billing").e("未获取到有效的 offerToken，无法发起购买")
                    LoadingUtils.dismissLoading()
                    return@queryProducts
                }
                // 构建Google Play支付参数
                val params = BillingFlowParams.newBuilder()
                    .setProductDetailsParamsList(
                        listOf(
                            BillingFlowParams.ProductDetailsParams.newBuilder()
                                .setProductDetails(productDetails)
                                .setOfferToken(offerToken)
                                .build()
                        )
                    )
                    .setIsOfferPersonalized(false) // 默认无个性化价格，如需支持可动态调整
                    .setObfuscatedAccountId(orderNo) // 设置订单号，用于后续验证
                    .build()

                // 记录启动支付的日志
                LogReportManager.reportPurchaseEvent(
                    orderId = orderNo,
                    event = PurchaseEventType.LAUNCH_PAY,
                    code = goodsCode,
                )

                // 启动Google Play支付页面
                GooglePlayBillingManager.launchPurchasePage(
                    activity = activity,
                    params = params,
                    onSuccess = { purchase, errCode ->
                        // 支付页面返回结果处理
                        if (purchase == null) {
                            // 支付失败或用户取消
                            LogReportManager.reportPurchaseEvent(
                                orderId = orderNo,
                                event = PurchaseEventType.LAUNCH_PAY_RESPONSE,
                                code = goodsCode,
                                result = Constant.FAIL,
                                resultCode = errCode
                            )
                            return@launchPurchasePage
                        }

                        // 支付成功，记录日志
                        LogReportManager.reportPurchaseEvent(
                            orderId = orderNo,
                            event = PurchaseEventType.LAUNCH_PAY_RESPONSE,
                            code = goodsCode,
                        )

                        // 开始验证订单
                        LogReportManager.reportPurchaseEvent(
                            orderId = orderNo,
                            event = PurchaseEventType.VERIFY_ORDER,
                            code = goodsCode,
                        )
                        verifyGooglePlayPaymentWithRetry(
                            orderNo = orderNo,
                            purchase = purchase,
                            goodsName = goodsName,
                            isSubscription = isSubscription,
                            onSuccess = {
                                LogReportManager.reportPurchaseEvent(
                                    orderId = orderNo,
                                    event = PurchaseEventType.VERIFY_ORDER_RESPONSE,
                                    code = goodsCode,
                                )

                                if (isSubscription) {
                                    LogReportManager.reportPurchaseEvent(
                                        event = PurchaseEventType.ACKNOWLEDGED_ORDER,
                                        orderId = orderNo,
                                        code = goodsCode,
                                    )
                                    GooglePlayBillingManager.acknowledgePurchase(
                                        purchaseToken = purchase.purchaseToken,
                                        onAcknowledged = {
                                            // 确认订阅充值成功
                                            handleRechargeSuccessBefore(orderNo,data.paidAmount!!,data.paidCurrency!!,true)

                                            LogReportManager.reportPurchaseEvent(
                                                orderId = orderNo,
                                                event = PurchaseEventType.ACKNOWLEDGED_ORDER_RESPONSE,
                                                code = goodsCode,
                                            )
                                        },
                                        onFail = { errCode, msg ->
                                            LogReportManager.reportPurchaseEvent(
                                                orderId = orderNo,
                                                event = PurchaseEventType.ACKNOWLEDGED_ORDER_RESPONSE,
                                                code = goodsCode,
                                                result = Constant.FAIL,
                                                resultCode = errCode
                                            )
                                        }
                                    )
                                } else {
                                    LogReportManager.reportPurchaseEvent(
                                        orderId = orderNo,
                                        event = PurchaseEventType.CONSUME_ORDER,
                                        code = goodsCode,
                                    )
                                    GooglePlayBillingManager.consumePurchase(
                                        purchaseToken = purchase.purchaseToken,
                                        onConsumed = {
                                            // 确认消费充值成功
                                            handleRechargeSuccessBefore(orderNo,data.paidAmount!!,data.paidCurrency!!,false)

                                            LogReportManager.reportPurchaseEvent(
                                                orderId = orderNo,
                                                event = PurchaseEventType.CONSUME_ORDER_RESPONSE,
                                                code = goodsCode,
                                            )
                                        },
                                        onFail = { errCode, msg ->
                                            LogReportManager.reportPurchaseEvent(
                                                orderId = orderNo,
                                                event = PurchaseEventType.CONSUME_ORDER_RESPONSE,
                                                code = goodsCode,
                                                result = Constant.FAIL,
                                                resultCode = errCode
                                            )
                                        }
                                    )
                                }


                            },
                            onFail = { errCode ->
                                LogReportManager.reportPurchaseEvent(
                                    orderId = orderNo,
                                    event = PurchaseEventType.VERIFY_ORDER_RESPONSE,
                                    code = goodsCode,
                                    result = Constant.FAIL,
                                    resultCode = errCode
                                )

                                // 5分钟检查一次
                                ThreadUtils.runOnBackgroundDelayed(POLL_INTERVAL) {
                                    checkPendingOrdersWithGooglePlay()
                                }


                                handleRechargeFailed(orderNo = orderNo, goodsName = goodsName)
//                                        ToastUtils.showToast("Order verification failed, please contact customer service, order number: $orderNo")
                                ToastUtils.showToast(CallmeApplication.context.getString(R.string.order_verification_failed, orderNo))
                            }
                        )
                    },
                    onFail = { errCode, msg ->
                        LogReportManager.reportPurchaseEvent(
                            orderId = orderNo,
                            event = PurchaseEventType.LAUNCH_PAY_RESPONSE,
                            code = goodsCode,
                            result = Constant.SUCCESS,
                            resultCode = errCode
                        )

//                                ToastUtils.showToast("Purchase fail code: $errCode")
                        ToastUtils.showToast(CallmeApplication.context.getString(R.string.purchase_failed))
                    }
                )

                // 跳到购买页时应该取消loading
                LoadingUtils.dismissLoading()
            },
            onFail = { errCode ->
                LogReportManager.reportPurchaseEvent(
                    orderId = orderNo,
                    event = PurchaseEventType.REVIEW_ORDER_RESPONSE,
                    code = goodsCode,
                    result = Constant.FAIL,
                    resultCode = errCode
                )

                LoadingUtils.dismissLoading()
//                        ToastUtils.showToast("queryProducts fail code: $errCode")
                ToastUtils.showToast(CallmeApplication.context.getString(R.string.sys_error_try_again))
            }
        )
    }


    /**
     * 显示支付方式选择弹窗
     *
     * 为广告来源用户提供多种支付方式选择，提高支付成功率。
     * 弹窗会显示可用的支付渠道，用户选择后回调相应的支付方式。
     *
     * @param activity 当前Activity上下文
     * @param goodsCode 商品编码，用于获取商品信息
     * @param goodsName 商品名称
     * @param onSelected 用户选择支付方式后的回调函数
     */
    private fun showPaymentMethodDialog(
        activity: Context,
        goodsCode: String,
        goodsName: String? = null,
        onSelected: (String) -> Unit = {}
    ) {
        if (activity is FragmentActivity) {
            // 先获取商品信息，用于在弹窗中显示商品详情
            val goodsInfo = findGoodsInfoByCode(goodsCode)
            if (goodsInfo != null) {
                // 创建并显示支付方式选择弹窗
                PaymentMethodDialog(goodsInfo) { payChannel ->
                    onSelected.invoke(payChannel)
                }.show(activity.supportFragmentManager, "payment_method_dialog")
            } else {
                // 如果找不到商品信息，使用默认支付渠道
                // 这种情况通常不应该发生，但作为兜底处理
                val defaultChannel = PaymentMethodManager.getDefaultPaymentMethod()
                if (defaultChannel != null) {
                    onSelected.invoke(defaultChannel)
                }
            }
        }
    }

    /**
     * 根据商品编码查找商品信息
     *
     * 从缓存的商品列表中查找指定编码的商品信息。
     * 这个方法用于在显示支付弹窗时获取商品详情。
     *
     * @param goodsCode 商品编码
     * @return 找到的商品信息，如果未找到则返回null
     */
    private fun findGoodsInfoByCode(goodsCode: String): com.score.callmetest.network.GoodsInfo? {
        val allGoods = GoodsManager.getCachedAllGoods()
        return allGoods.find { it.code == goodsCode }
    }

    /**
     * 创建充值订单
     *
     * 向服务器发送创建订单请求，获取订单号和支付相关信息。
     * 这是充值流程的第一步，成功后会根据支付渠道进行相应的支付处理。
     *
     * @param goodsCode 商品编码
     * @param payChannel 支付渠道
     * @param invitationId 邀请ID，用于推广追踪
     * @param entry 充值入口来源
     * @param onSuccess 创建成功的回调，返回订单信息
     * @param onFail 创建失败的回调，返回错误码和错误信息
     */
    private fun createRechargeOrder(
        goodsCode: String,
        payChannel: String,
        invitationId: String? = null,
        entry: RechargeSource,
        onSuccess: ((CreateRechargeResponse) -> Unit)? = null,
        onFail: ((Int, String?) -> Unit)? = null,
    ) {
        // 记录创建订单事件
        LogReportManager.reportPurchaseEvent(
            event = PurchaseEventType.CREATE_ORDER,
            code = goodsCode,
        )

        // 在IO线程中执行网络请求
        CoroutineScope(Dispatchers.IO).launch {
            try {
                // 构建创建订单的请求参数
                val request = CreateRechargeRequest(
                    goodsCode = goodsCode,
                    payChannel = payChannel,
                    source = invitationId,
                    entry = entry.source
                )

                // 发送创建订单请求
                val response = RetrofitUtils.dataRepository.createRechargeOrder(request)

                // 切换到主线程处理响应结果
                withContext(Dispatchers.Main) {
                    if (response is NetworkResult.Success && response.data != null) {
                        // 订单创建成功
                        LogReportManager.reportPurchaseEvent(
                            event = PurchaseEventType.CREATE_ORDER_RESPONSE,
                            code = goodsCode,
                            orderId = response.data.orderNo
                        )
                        onSuccess?.invoke(response.data)
                    } else {
                        // 订单创建失败
                        LogReportManager.reportPurchaseEvent(
                            event = PurchaseEventType.CREATE_ORDER_RESPONSE,
                            code = goodsCode,
                            result = Constant.FAIL,
                            resultCode = BillingResponseCode.NETWORK_ERROR
                        )

                        // 根据响应类型返回相应的错误信息
                        if(response is NetworkResult.Error){
                            onFail?.invoke(response.code?:-1, response.message)
                        }else {
                            onFail?.invoke(-1, "unknown error")
                        }
                    }
                }
            } catch (e: Exception) {
                // 网络请求异常处理
                LogReportManager.reportPurchaseEvent(
                    event = PurchaseEventType.CREATE_ORDER_RESPONSE,
                    code = goodsCode,
                    result = Constant.FAIL,
                    resultCode = BillingResponseCode.NETWORK_ERROR
                )

                withContext(Dispatchers.Main) {
                    onFail?.invoke(BillingResponseCode.NETWORK_ERROR, e.message)
                }
            }
        }
    }

    /**
     * 校验Google Play订单的核心方法
     *
     * 向服务器发送Google Play支付验证请求，确认支付的有效性。
     * 这是防止支付欺诈和确保订单真实性的重要步骤。
     *
     * @param orderNo 订单号
     * @param purchase Google Play返回的购买信息
     * @param isSubscription 是否为订阅商品
     * @return 验证是否成功
     */
    private suspend fun verifyGooglePlayOrder(
        orderNo: String,
        purchase: Purchase,
        isSubscription: Boolean = false
    ): Boolean {
        // 构建验证请求，包含购买数据和签名
        val verifyReq = GooglePlayPaymentVerifyRequest(
            orderNo = orderNo,
            purchaseData = purchase.originalJson, // Google Play返回的原始购买数据
            signature = purchase.signature // Google Play的数字签名
        )

        // 发送验证请求到服务器
        val verifyResp = RetrofitUtils.dataRepository.verifyGooglePlayPayment(verifyReq)

        return verifyResp is NetworkResult.Success && verifyResp.data == true
    }

    /**
     * Google Play支付校验（带重试机制）
     *
     * 由于网络不稳定等原因，支付验证可能失败，因此提供重试机制。
     * 最多重试5次，每次间隔1秒，提高验证成功率。
     *
     * @param orderNo 订单号
     * @param goodsName 商品名称
     * @param purchase Google Play购买信息
     * @param isSubscription 是否为订阅商品
     * @param maxRetry 最大重试次数，默认5次
     * @param delayMillis 重试间隔时间，默认1秒
     * @param onSuccess 验证成功的回调
     * @param onFail 验证失败的回调，返回错误码
     */
    private fun verifyGooglePlayPaymentWithRetry(
        orderNo: String,
        goodsName: String? = null,
        purchase: Purchase,
        isSubscription: Boolean = false,
        maxRetry: Int = 5,
        delayMillis: Long = 1000L,
        onSuccess: () -> Unit = {},
        onFail: (Int) -> Unit = {},
    ) {
        if(mIsLogout.get()) return
        CoroutineScope(Dispatchers.IO).launch {
            var success = false

            // 重试验证逻辑
            repeat(maxRetry) { attempt ->
                if(mIsLogout.get()) return@launch
                try {
                    success = verifyGooglePlayOrder(orderNo, purchase, isSubscription)
                    if (success) {
                        // 验证成功，切换到主线程执行回调
                        withContext(Dispatchers.Main) {
                            onSuccess.invoke()
                        }
                        return@launch
                    }
                } catch (e: Exception) {
                    Timber.tag("dsc--Recharge")
                        .e("Google Play 支付校验异常: ${e.message}, attempt=${attempt + 1}")
                }

                Timber.tag("dsc--Recharge")
                    .w("Google Play 校验重试: $orderNo, attempt=${attempt + 1}")

                // 如果不是最后一次重试，等待指定时间后继续
                if (attempt < maxRetry - 1) delay(delayMillis)
            }

            // 所有重试都失败
            if (!success) {
                withContext(Dispatchers.Main) {
                    onFail.invoke(BillingResponseCode.NETWORK_ERROR)
                }
                Timber.tag("dsc--Recharge")
                    .e("订单校验失败，请联系客服，订单号：$orderNo")
            }
        }
    }

    /**
     * 处理充值成功前的数据上报
     *
     * 在显示充值成功UI之前，先进行必要的数据上报和统计。
     * 包括向第三方平台（Adjust、Facebook）报告支付成功事件。
     *
     * @param orderNo 订单号
     * @param revenue 收入金额
     * @param currency 货币类型
     * @param isSubscription 是否为订阅商品
     */
    fun handleRechargeSuccessBefore(
        orderNo: String,
        revenue: Double,
        currency: String,
        isSubscription: Boolean
    ) {
        if(mIsLogout.get()) return
        if(mCacheSuccessOrder.contains(orderNo)){
            return
        }
        mCacheSuccessOrder.add(orderNo)
        // 处理充值成功的UI显示
        handleRechargeSuccess(orderNo = orderNo)

        // 向Adjust报告支付成功事件，用于广告效果追踪
        AdjustManager.reportPaySuccessEvent(
            orderId = orderNo,
            revenue = revenue,
            currency = currency
        )

        // 向Facebook报告购买事件，用于广告优化
        FacebookManager.logPurchaseEvent(revenue,currency)
    }

    /**
     * 处理充值成功的UI显示
     *
     * 显示充值成功的弹窗，提示用户充值正在处理中。
     * 同时刷新用户信息，更新金币余额。
     *
     * @param orderNo 订单号
     */
    private fun handleRechargeSuccess(orderNo: String) {
        // 刷新用户金币余额，确保显示最新的账户信息
        UserInfoManager.refreshMyUserInfo()

        Timber.tag("dsc--Recharge").i("充值成功，订单号：$orderNo")

        // 构建充值成功提示文案
        val time = "3 mins"
        val content =
            "We are doing our best to recharge your account. It may take about ${time}. Please be patient."
        val spannable = android.text.SpannableString(content)

        // 高亮显示时间部分
        val startIndex = content.indexOf(time.toString())
        val endIndex = startIndex + time.toString().length
        spannable.setSpan(
            android.text.style.ForegroundColorSpan("#FF7F37".toColorInt()),
            startIndex,
            endIndex,
            android.text.Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        // 获取当前顶层Activity并显示充值成功弹窗
        ActivityUtils.getTopActivity()?.let { activity ->
            BaseCustomDialog(
                activity,
                emojiSvgaResString = "pay_success.svga", // 支付成功动画
                title = "Payment Successful!",
                content = spannable,
                agreeText = "Refresh", // 刷新按钮，用于立即查看充值结果
                agreeBg = DrawableUtils.createRoundRectDrawable(
                    Color.BLACK,
                    DisplayUtils.dp2pxInternalFloat(30f)
                ),
                agreeTextColor = Color.WHITE,
                cancelText = "OK", // 确定按钮
                cancelBg = DrawableUtils.createRoundRectDrawable(
                    "#F3F3F3".toColorInt(),
                    DisplayUtils.dp2pxInternalFloat(30f)
                ),
                cancelTextColor = Color.BLACK,
                bgRes = R.drawable.dialog_bg_green, // 绿色背景表示成功
                onAgree = {
                    // 点击刷新按钮：获取用户信息并跳转到聊天页面
                    // 先查缓存，再网络查询，确保获取到最新的用户信息
                    UserInfoManager.getUserInfo(StrategyManager.strategyConfig?.userServiceAccountId){ getUserInfo ->
                        getUserInfo?.let { nonNullUser ->
                            ChatActivity.start(activity, nonNullUser)
                        }
                    }
                },
                onCancel = {
                    // 点击确定按钮：关闭弹窗，不做其他操作
                }
            ).show()
        }
    }

    /**
     * 处理充值失败的UI显示
     *
     * 当支付验证失败时，显示相应的提示弹窗。
     * 告知用户订单正在处理中，如果确认已支付但未到账，可联系客服。
     *
     * @param orderNo 订单号
     * @param goodsName 商品名称
     */
    private fun handleRechargeFailed(orderNo: String, goodsName: String? = null) {
        if(mIsLogout.get()) return
        Timber.tag("dsc--Recharge").e("充值失败，订单号：$orderNo")
        if(mCacheFailedOrder.contains(orderNo)){
            return
        }
        mCacheFailedOrder.add(orderNo)

        // 构建失败提示文案
        val title = "Your $goodsName order is processing. We are verifying store payment."
        val content =
            "Contact Customer Service If you've confirmed payment but get no coins in 3 mins."

        ActivityUtils.getTopActivity()?.let { activity ->
            BaseCustomDialog(
                activity,
                emojiResId = R.drawable.pay_fail, // 支付失败图标
                title = SpannableString(title).apply {
                    // 高亮显示商品名称
                    val startIndex = title.indexOf(goodsName.toString())
                    val endIndex = startIndex + goodsName.toString().length
                    setSpan(
                        android.text.style.ForegroundColorSpan("#FF7F37".toColorInt()),
                        startIndex,
                        endIndex,
                        android.text.Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                },
                content = content,
                agreeText = "Customer Service", // 联系客服按钮
                agreeBg = DrawableUtils.createRoundRectDrawable(
                    Color.BLACK,
                    DisplayUtils.dp2pxInternalFloat(30f)
                ),
                cancelText = "OK", // 确定按钮
                bgRes = R.drawable.dialog_bg_green,
                onAgree = {
                    // 可以跳转到客服页面或打开客服聊天
                    UserInfoManager.getUserInfo(StrategyManager.strategyConfig?.userServiceAccountId){ getUserInfo ->
                        getUserInfo?.let { nonNullUser ->
                            ChatActivity.start(activity, nonNullUser)
                        }
                    }
                }
            ).apply {
                // 为客服按钮添加图标
                getAgreeView().apply {
                    setCompoundDrawablesWithIntrinsicBounds(
                        AppCompatResources.getDrawable(
                            activity,
                            R.drawable.customer_service_white
                        ), null, null, null
                    )
                    compoundDrawablePadding = DisplayUtils.dp2pxInternal(8f)
                }
                show()
            }
        }
    }

    /**
     * 开启轮询订单状态检查
     *
     * 在用户登录时自动开启，使用默认间隔（5分钟）进行无限轮询
     */
    private fun startPolling() {
        stopPolling() // 先停止之前的轮询
        mCurrentPollingInterval = POLL_INTERVAL
        mCurrentPollingTimes = -1
        mExecutedPollingTimes = 0
        mPollingStrategyVersion = System.currentTimeMillis() // 生成新的策略版本号
        pollCheckOrderStatus()
        Timber.tag("RechargeManager").d("开启订单状态轮询，间隔: ${mCurrentPollingInterval}ms，策略版本: $mPollingStrategyVersion")
    }

    /**
     * 停止轮询订单状态检查
     *
     * 在用户登出时调用，取消所有轮询任务
     */
    private fun stopPolling() {
        mPollingJob?.cancel()
        mPollingJob = null
        mExecutedPollingTimes = 0
        mPollingStrategyVersion = 0L // 重置策略版本号
        Timber.tag("RechargeManager").d("停止订单状态轮询")
    }

    /**
     * 动态调整轮询间隔和次数
     *
     * 可以随时调用此方法调整轮询策略，会立即中断当前轮询并开启新的轮询
     *
     * @param interval 轮询间隔（毫秒），-1表示使用默认间隔（5分钟）
     * @param times 轮询次数，-1表示无限轮询
     */
    fun adjustPollingStrategy(interval: Long = -1, times: Int = -1) {
        if (mIsLogout.get()) {
            Timber.tag("RechargeManager").w("用户未登录，无法调整轮询策略")
            return
        }

        // 设置新的轮询参数
        mCurrentPollingInterval = if (interval > 0) interval else POLL_INTERVAL
        mCurrentPollingTimes = times
        mExecutedPollingTimes = 0
        mPollingStrategyVersion = System.currentTimeMillis() // 生成新的策略版本号

        // 立即中断当前轮询并开启新轮询
        stopPolling()
        pollCheckOrderStatus()

        val timesDesc = if (times == -1) "无限次" else "${times}次"
        Timber.tag("RechargeManager").d("调整轮询策略 - 间隔: ${mCurrentPollingInterval}ms, 次数: $timesDesc, 策略版本: $mPollingStrategyVersion")
    }

    /**
     * 轮询查询订单状态---三方，谷歌订单不在这里查询
     *
     * 实现逻辑：
     * 1. 检查是否已登出，如果登出则停止轮询
     * 2. 检查是否达到指定轮询次数，如果达到则转为默认间隔的无限轮询
     * 3. 执行订单状态检查（预留位置供您实现具体逻辑）
     * 4. 安排下次轮询
     */
    private fun pollCheckOrderStatus() {
        // 检查是否已登出
        if (mIsLogout.get()) {
            Timber.tag("RechargeManager").d("用户已登出，停止轮询")
            return
        }

        // 记录当前策略版本号
        val currentStrategyVersion = mPollingStrategyVersion

        // 检查是否有订单需要轮询
        if (mCacheOrderInfoMap.isEmpty()) {
            // 没有订单时，延迟后继续轮询（保持轮询活跃状态）
            scheduleNextPolling(currentStrategyVersion)
            return
        }

        // 检查是否达到指定轮询次数
        if (mCurrentPollingTimes > 0 && mExecutedPollingTimes >= mCurrentPollingTimes) {
            // 达到指定次数，转为默认间隔的无限轮询
            Timber.tag("RechargeManager").d("达到指定轮询次数(${mCurrentPollingTimes})，转为默认间隔无限轮询")
            mCurrentPollingInterval = POLL_INTERVAL
            mCurrentPollingTimes = -1
            mExecutedPollingTimes = 0
        }

        // 执行实际的订单状态检查
        mPollingJob = CoroutineScope(Dispatchers.IO).launch {
            try {
                // TODO: 在这里实现具体的订单状态检查逻辑
                // 您可以在这里添加检查 mCacheOrderInfoMap 中订单状态的具体实现
                checkOrderStatusImpl(currentStrategyVersion)

                // 检查策略版本是否仍然有效（避免处理过期的结果）
                if (currentStrategyVersion == mPollingStrategyVersion) {
                    // 增加已执行次数
                    mExecutedPollingTimes++
                    Timber.tag("RechargeManager").d("执行订单状态检查，第${mExecutedPollingTimes}次，订单数量: ${mCacheOrderInfoMap.size}")
                } else {
                    Timber.tag("RechargeManager").d("轮询策略已更新，忽略过期结果 (版本: $currentStrategyVersion -> $mPollingStrategyVersion)")
                }

            } catch (e: Exception) {
                if (currentStrategyVersion == mPollingStrategyVersion) {
                    Timber.tag("RechargeManager").e(e, "订单状态检查异常")
                } else {
                    Timber.tag("RechargeManager").d("轮询策略已更新，忽略过期异常 (版本: $currentStrategyVersion)")
                }
            } finally {
                // 安排下次轮询（只有当前策略版本仍然有效时）
                if (!mIsLogout.get() && currentStrategyVersion == mPollingStrategyVersion) {
                    scheduleNextPolling(currentStrategyVersion)
                }
            }
        }
    }

    /**
     * 安排下次轮询
     *
     * @param strategyVersion 策略版本号，用于检查轮询策略是否仍然有效
     */
    private fun scheduleNextPolling(strategyVersion: Long) {
        if (mIsLogout.get()) return

        mPollingJob = CoroutineScope(Dispatchers.IO).launch {
            delay(mCurrentPollingInterval)
            // 检查策略版本是否仍然有效
            if (!mIsLogout.get() && strategyVersion == mPollingStrategyVersion) {
                pollCheckOrderStatus()
            } else {
                Timber.tag("RechargeManager").d("轮询策略已更新，取消下次轮询 (版本: $strategyVersion -> $mPollingStrategyVersion)")
            }
        }
    }

    /**
     * 订单状态检查的具体实现
     *
     * @param strategyVersion 当前轮询策略的版本号，用于判断轮询是否仍然有效
     *
     * TODO: 请在这里实现具体的订单状态检查逻辑
     * 可以遍历 mCacheOrderInfoMap 中的订单，调用相应的API检查状态
     */
    private suspend fun checkOrderStatusImpl(strategyVersion: Long) {
        // 预留位置：具体的订单状态检查实现
        // 示例代码结构：
        /*
        mCacheOrderInfoMap.forEach { (orderNo, orderInfo) ->
            try {
                // 在每次网络请求前检查轮询策略是否仍然有效
                if (strategyVersion != mPollingStrategyVersion) {
                    Timber.tag("RechargeManager").d("轮询策略已更新，停止处理订单 $orderNo (版本: $strategyVersion -> $mPollingStrategyVersion)")
                    return@forEach // 跳过当前订单的处理
                }

                // 调用API检查订单状态
                val orderStatus = checkSingleOrderStatus(orderNo)

                // 再次检查轮询策略是否仍然有效（网络请求完成后）
                if (strategyVersion != mPollingStrategyVersion) {
                    Timber.tag("RechargeManager").d("轮询策略已更新，忽略订单 $orderNo 的结果 (版本: $strategyVersion -> $mPollingStrategyVersion)")
                    return@forEach // 忽略结果
                }

                // 根据状态处理订单
                when (orderStatus) {
                    "SUCCESS" -> {
                        // 订单成功，处理成功逻辑
                        handleOrderSuccess(orderNo, orderInfo)
                        // 从缓存中移除已完成的订单
                        removeOrderInfoFromSp(orderNo)
                    }
                    "FAILED" -> {
                        // 订单失败，处理失败逻辑
                        handleOrderFailed(orderNo, orderInfo)
                        // 从缓存中移除失败的订单
                        removeOrderInfoFromSp(orderNo)
                    }
                    "PENDING" -> {
                        // 订单处理中，继续等待
                        Timber.tag("RechargeManager").d("订单 $orderNo 仍在处理中")
                    }
                }
            } catch (e: Exception) {
                // 检查轮询策略是否仍然有效
                if (strategyVersion == mPollingStrategyVersion) {
                    Timber.tag("RechargeManager").e(e, "检查订单 $orderNo 状态失败")
                } else {
                    Timber.tag("RechargeManager").d("轮询策略已更新，忽略订单 $orderNo 的异常 (版本: $strategyVersion)")
                }
            }
        }
        */

        // 当前为空实现，等待您添加具体逻辑
        Timber.tag("RechargeManager").d("订单状态检查实现位置 - 待实现具体逻辑，策略版本: $strategyVersion")

        // 模拟网络请求延迟，用于测试策略版本检查
        delay(100)

        // 检查策略版本是否仍然有效
        if (strategyVersion != mPollingStrategyVersion) {
            Timber.tag("RechargeManager").d("轮询策略已更新，停止当前检查 (版本: $strategyVersion -> $mPollingStrategyVersion)")
            return
        }
    }

    /**
     * 检查当前轮询策略是否仍然有效
     *
     * 在您的网络请求实现中，可以使用此方法来判断是否需要继续处理结果
     *
     * @param strategyVersion 开始处理时的策略版本号
     * @return true表示策略仍然有效，false表示策略已更新，应该停止处理
     */
    fun isPollingStrategyValid(strategyVersion: Long): Boolean {
        return strategyVersion == mPollingStrategyVersion && !mIsLogout.get()
    }

    /**
     * 获取当前轮询策略信息
     *
     * @return 包含当前轮询间隔、次数、已执行次数和策略版本的信息
     */
    fun getCurrentPollingInfo(): String {
        val timesDesc = if (mCurrentPollingTimes == -1) "无限次" else "${mCurrentPollingTimes}次"
        return "间隔: ${mCurrentPollingInterval}ms, 总次数: $timesDesc, 已执行: ${mExecutedPollingTimes}次, 策略版本: $mPollingStrategyVersion"
    }


    /**
     * 检查并处理Google Play未完成的订单（自动补单机制）
     *
     * 这个方法用于处理以下情况：
     * 1. 用户支付成功但应用崩溃，导致订单未完成确认/消费
     * 2. 网络问题导致的订单验证失败
     * 3. 其他异常情况导致的订单状态不一致
     *
     * 通过查询Google Play的未消费商品和未确认订阅，自动完成补单流程。
     */
    fun checkPendingOrdersWithGooglePlay() {
        if(mIsLogout.get()) return
        withBillingClientReady {
            // 查询所有未消费的购买记录
            GooglePlayBillingManager.queryUnconsumedPurchases { purchase, isSubscription ->
                // 获取订单号（从obfuscatedAccountId中获取）
                val orderNo = purchase.accountIdentifiers?.obfuscatedAccountId

                // 如果没有订单号，说明不是我们的订单，跳过处理
                if (orderNo.isNullOrEmpty()) return@queryUnconsumedPurchases

                // 重新验证订单并完成相应的确认/消费流程
                verifyGooglePlayPaymentWithRetry(
                    orderNo = orderNo,
                    purchase = purchase,
                    isSubscription = isSubscription,
                    onSuccess = {
                        // 验证成功后，根据商品类型进行相应处理
                        if (isSubscription) {
                            // 订阅商品需要确认（acknowledge）
                            GooglePlayBillingManager.acknowledgePurchase(purchase.purchaseToken)
                        } else {
                            // 一次性商品需要消费（consume）
                            GooglePlayBillingManager.consumePurchase(purchase.purchaseToken)
                        }
                    },
                    onFail = {
                        // 验证失败，5分钟后再次检查
                        // 这样可以处理临时的网络问题或服务器问题
                        ThreadUtils.runOnBackgroundDelayed(POLL_INTERVAL) {
                            checkPendingOrdersWithGooglePlay()
                        }
                    }
                )
            }
        }
    }

    /**
     * 确保Google Play Billing客户端已准备就绪的统一入口
     *
     * 在执行任何Google Play相关操作之前，都需要确保BillingClient已连接。
     * 这个方法提供了统一的初始化检查机制。
     *
     * @param block 在BillingClient准备就绪后执行的代码块
     */
    private fun withBillingClientReady(block: () -> Unit) {
        GooglePlayBillingManager.ensureReady {
            block()
        }
    }
} 