package com.score.callmetest.manager

import com.score.callmetest.util.ThreadUtils
import kotlinx.coroutines.Job
import timber.log.Timber

/**
 * 全局倒计时管理器
 * 用于统一管理不同类型活动的倒计时状态，确保在不同页面间保持一致
 */
object CountdownManager {

    // 活动类型枚举
    enum class ActivityType {
        GOODS_PROMOTION,        // 商品促销（RechargeOptionAdapter）
        PRESENTED_COINS,        // 新人促销（HomeFragment）
        SPECIAL_PROMOTION       // 特殊活动促销（HomeFragment）
    }

    // 存储每个活动的倒计时信息，key格式："{activityType}_{activityId}"
    private val countdownStates = mutableMapOf<String, CountdownState>()

    // 存储每个活动的倒计时Job
    private val countdownJobs = mutableMapOf<String, Job>()
    
    // 倒计时状态数据类
    data class CountdownState(
        val activityType: ActivityType,
        val activityId: String,
        val startTime: Long, // 倒计时开始的系统时间
        val totalDuration: Long, // 总倒计时时长（毫秒）
        var isExpired: Boolean = false
    ) {
        // 计算当前剩余时间
        fun getCurrentRemainingTime(): Long {
            if (isExpired) return 0L
            val elapsed = System.currentTimeMillis() - startTime
            val remaining = totalDuration - elapsed
            return if (remaining > 0) remaining else 0L
        }
        
        // 检查是否已过期
        fun checkExpired(): Boolean {
            if (!isExpired && getCurrentRemainingTime() <= 0) {
                isExpired = true
            }
            return isExpired
        }
    }
    
    /**
     * 生成活动的唯一键
     */
    private fun generateKey(activityType: ActivityType, activityId: String): String {
        return "${activityType.name}_$activityId"
    }

    /**
     * 初始化或更新活动的倒计时状态
     * @param activityType 活动类型
     * @param activityId 活动ID（商品编码、促销ID等）
     * @param remainMilliseconds 服务器返回的剩余毫秒数
     */
    fun initOrUpdateCountdown(activityType: ActivityType, activityId: String, remainMilliseconds: Long?) {
        if (activityId.isEmpty() || remainMilliseconds == null || remainMilliseconds <= 0) {
            return
        }

        val key = generateKey(activityType, activityId)
        val existingState = countdownStates[key]

        // 如果已存在且未过期，不重新初始化
        if (existingState != null && !existingState.checkExpired()) {
            Timber.tag("CountdownManager: ").d("活动 $key 倒计时已存在，剩余时间: ${existingState.getCurrentRemainingTime()}ms")
            return
        }

        // 创建新的倒计时状态
        val newState = CountdownState(
            activityType = activityType,
            activityId = activityId,
            startTime = System.currentTimeMillis(),
            totalDuration = remainMilliseconds
        )

        countdownStates[key] = newState
        Timber.tag("CountdownManager: ").d("初始化活动 $key 倒计时，总时长: ${remainMilliseconds}ms")
    }
    
    /**
     * 获取活动的当前剩余时间
     * @param activityType 活动类型
     * @param activityId 活动ID
     * @return 剩余毫秒数，如果不存在或已过期返回0
     */
    fun getCurrentRemainingTime(activityType: ActivityType, activityId: String): Long {
        val key = generateKey(activityType, activityId)
        val state = countdownStates[key] ?: return 0L
        return state.getCurrentRemainingTime()
    }

    /**
     * 检查活动倒计时是否已过期
     * @param activityType 活动类型
     * @param activityId 活动ID
     * @return true表示已过期或不存在
     */
    fun isExpired(activityType: ActivityType, activityId: String): Boolean {
        val key = generateKey(activityType, activityId)
        val state = countdownStates[key] ?: return true
        return state.checkExpired()
    }
    
    /**
     * 启动活动的倒计时
     * @param activityType 活动类型
     * @param activityId 活动ID
     * @param onTick 每秒回调，参数为剩余毫秒数
     * @param onFinish 倒计时结束回调
     * @return Job 可用于取消倒计时
     */
    fun startCountdown(
        activityType: ActivityType,
        activityId: String,
        onTick: (remainingMillis: Long) -> Unit,
        onFinish: () -> Unit
    ): Job? {
        val key = generateKey(activityType, activityId)
        val state = countdownStates[key]
        if (state == null || state.checkExpired()) {
            Timber.tag("CountdownManager: ").w("活动 $key 倒计时不存在或已过期")
            try {
                onFinish()
            } catch (e: Exception) {
                Timber.tag("CountdownManager: ").e(e, "执行onFinish回调失败: $key")
            }
            return null
        }

        // 取消之前的倒计时Job
        countdownJobs[key]?.cancel()

        val currentRemaining = state.getCurrentRemainingTime()
        if (currentRemaining <= 0) {
            state.isExpired = true
            try {
                onFinish()
            } catch (e: Exception) {
                Timber.tag("CountdownManager: ").e(e, "执行onFinish回调失败: $key")
            }
            return null
        }

        val job = ThreadUtils.startCountdownMillis(
            totalMilliseconds = currentRemaining,
            intervalMillis = 1000L,
            onTick = { remainingMillis ->
                try {
                    onTick(remainingMillis)
                } catch (e: Exception) {
                    Timber.tag("CountdownManager: ").e(e, "执行onTick回调失败: $key, remainingMillis: $remainingMillis")
                }
            },
            onFinish = {
                try {
                    state.isExpired = true
                    countdownJobs.remove(key)
                    onFinish()
                } catch (e: Exception) {
                    Timber.tag("CountdownManager: ").e(e, "执行onFinish回调失败: $key")
                }
            }
        )

        countdownJobs[key] = job
        Timber.tag("CountdownManager: ").d("启动活动 $key 倒计时，当前剩余: ${currentRemaining}ms")
        return job
    }
    
    /**
     * 停止活动的倒计时
     * @param activityType 活动类型
     * @param activityId 活动ID
     */
    fun stopCountdown(activityType: ActivityType, activityId: String) {
        val key = generateKey(activityType, activityId)
        countdownJobs[key]?.cancel()
        countdownJobs.remove(key)
        Timber.d("CountdownManager: 停止活动 $key 倒计时")
    }

    /**
     * 移除活动的倒计时状态
     * @param activityType 活动类型
     * @param activityId 活动ID
     */
    fun removeCountdown(activityType: ActivityType, activityId: String) {
        stopCountdown(activityType, activityId)
        val key = generateKey(activityType, activityId)
        countdownStates.remove(key)
        Timber.d("CountdownManager: 移除活动 $key 倒计时状态")
    }
    
    /**
     * 清理所有已过期的倒计时状态
     */
    fun cleanupExpiredCountdowns() {
        val expiredCodes = countdownStates.filter { it.value.checkExpired() }.keys
        expiredCodes.forEach { removeCountdown(it) }
        if (expiredCodes.isNotEmpty()) {
            Timber.d("CountdownManager: 清理了 ${expiredCodes.size} 个过期倒计时")
        }
    }
    
    /**
     * 清理所有倒计时状态
     */
    fun clearAll() {
        countdownJobs.values.forEach { it.cancel() }
        countdownJobs.clear()
        countdownStates.clear()
        Timber.d("CountdownManager: 清理所有倒计时状态")
    }
    
    /**
     * 获取当前活跃的倒计时数量
     */
    fun getActiveCountdownCount(): Int {
        cleanupExpiredCountdowns()
        return countdownStates.size
    }

    // ========== 兼容性方法，用于商品促销 ==========

    /**
     * 初始化或更新商品促销的倒计时状态（兼容性方法）
     * @param goodsCode 商品编码
     * @param remainMilliseconds 服务器返回的剩余毫秒数
     */
    fun initOrUpdateCountdown(goodsCode: String, remainMilliseconds: Long?) {
        initOrUpdateCountdown(ActivityType.GOODS_PROMOTION, goodsCode, remainMilliseconds)
    }

    /**
     * 获取商品促销的当前剩余时间（兼容性方法）
     * @param goodsCode 商品编码
     * @return 剩余毫秒数，如果不存在或已过期返回0
     */
    fun getCurrentRemainingTime(goodsCode: String): Long {
        return getCurrentRemainingTime(ActivityType.GOODS_PROMOTION, goodsCode)
    }

    /**
     * 检查商品促销倒计时是否已过期（兼容性方法）
     * @param goodsCode 商品编码
     * @return true表示已过期或不存在
     */
    fun isExpired(goodsCode: String): Boolean {
        return isExpired(ActivityType.GOODS_PROMOTION, goodsCode)
    }

    /**
     * 启动商品促销的倒计时（兼容性方法）
     * @param goodsCode 商品编码
     * @param onTick 每秒回调，参数为剩余毫秒数
     * @param onFinish 倒计时结束回调
     * @return Job 可用于取消倒计时
     */
    fun startCountdown(
        goodsCode: String,
        onTick: (remainingMillis: Long) -> Unit,
        onFinish: () -> Unit
    ): Job? {
        return startCountdown(ActivityType.GOODS_PROMOTION, goodsCode, onTick, onFinish)
    }

    /**
     * 停止商品促销的倒计时（兼容性方法）
     * @param goodsCode 商品编码
     */
    fun stopCountdown(goodsCode: String) {
        stopCountdown(ActivityType.GOODS_PROMOTION, goodsCode)
    }

    /**
     * 移除商品促销的倒计时状态（兼容性方法）
     * @param goodsCode 商品编码
     */
    fun removeCountdown(goodsCode: String) {
        removeCountdown(ActivityType.GOODS_PROMOTION, goodsCode)
    }
}
