package com.score.callmetest.ui.videocall

import android.app.ActivityManager
import android.content.Context
import android.os.Build
import android.provider.Settings
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentActivity
import androidx.lifecycle.LifecycleOwner
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.manager.AppLifecycleManager
import com.score.callmetest.manager.AppPermissionManager
import com.score.callmetest.manager.AudioPlayManager
import com.score.callmetest.manager.DualChannelEventManager
import com.score.callmetest.manager.HangUpReason
import com.score.callmetest.manager.LogReportManager
import com.score.callmetest.manager.OnCallMessage
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.manager.VideoCallManager
import com.score.callmetest.network.LiveCallAction
import com.score.callmetest.network.LiveCallExt
import com.score.callmetest.network.LiveCallExt2
import com.score.callmetest.ui.message.MessageIncomingManager
import com.score.callmetest.ui.widget.InsufficientBalanceDialog
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.AgodaUtils
import com.score.callmetest.util.ToastUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.lang.ref.WeakReference

/**
 * 管理来电事件
 */
object CallIncomingManager {
    private var popupViewRef: WeakReference<CallIncomingPopupView>? = null
    private var dialogFragmentRef: WeakReference<CallIncomingDialogFragment>? = null
    private var isShowing = false
    private var currentCallInfo: OnCallMessage? = null
    private var autoHangUpJob: Job? = null
    private val coroutineScope = MainScope()

    fun getCurrentCallInfo(): OnCallMessage? {
        return currentCallInfo
    }

    fun handleIncomingCall(context: Context, callInfo: OnCallMessage) {
        if (isShowing) return

        currentCallInfo = callInfo

        startAutoHangUpTimer()
        // 监听hangup事件（使用双通道去重）
        DualChannelEventManager.observeOnHangUp(context as LifecycleOwner) { onHangUpMessage ->
            handleReject(HangUpReason.CL_REMOTE_USER_LEFT)
        }

        // 开始播放铃声
        playRingtone(context)

        // 检查应用是否在后台
        if (AppLifecycleManager.isAppInBackground() && !canDrawOverlays(context)) {
            // TODO: 需要悬浮窗权限，才能重新回到前台。 应用在后台，直接跳转到来电界面
//            VideoCallActivity.startIncoming(CallmeApplication.context, callInfo)
            return
        }

        VideoCallActivity.startIncoming(context, callInfo)

        // 应用在前台，显示来电弹窗
//        showIncomingCallPopup(context, callInfo)
        isShowing = true
    }

    private fun showIncomingCallPopup(context: Context, callInfo: OnCallMessage) {
        // 来电弹窗优先级更高，顶掉消息弹窗
        MessageIncomingManager.onCallIncomingShown()

        startAutoHangUpTimer()

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M && !Settings.canDrawOverlays(context.applicationContext)) {
            // 无悬浮窗权限，使用DialogFragment方案
            if (context is FragmentActivity) {
                showDialogFragmentInternal(context, callInfo)
            } else {
                // context不是FragmentActivity，无法弹DialogFragment
            }
            return
        }

        // 有悬浮窗权限，使用WindowManager方案
        val appContext = context.applicationContext
        val windowManager = appContext.getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val popupView = CallIncomingPopupView(appContext)
        popupView.bind(
            avatarUrl = callInfo.avatarThumbUrl ?: callInfo.avatar,
            nicknameStr = callInfo.nickname,
            ageStr = callInfo.age?.toString(),
            countryStr = callInfo.country,
            price = callInfo.unitPrice.toString(),
            onAccept = {
                handleAccept(
                    onSuccess = { onCallMessage ->
                        VideoCallActivity.startOngoing(
                            context,
                            onCallMessage.channelName.toString(),
                            onCallMessage.toUserId.toString(),
                            onCallMessage.fromUserId.toString()
                        )
                    }
                )
            },
            onReject = { handleReject() },
            onContentClick = { handleContentClick() }
        )

        val params = WindowManager.LayoutParams(
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.MATCH_PARENT,
            WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE or WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON,
            android.graphics.PixelFormat.TRANSLUCENT
        )
        params.gravity = android.view.Gravity.TOP
        windowManager.addView(popupView, params)
        popupViewRef = WeakReference(popupView)
        isShowing = true
    }

    fun hideIncomingCall() {
        val popupView = popupViewRef?.get()
        val dialog = dialogFragmentRef?.get()
        if (isShowing && popupView != null) {
            popupViewRef = null
            isShowing = false
        } else if (isShowing && dialog != null) {
            dialog.dismissAllowingStateLoss()
            dialogFragmentRef = null
            isShowing = false
        } else {
            isShowing = false
            dialogFragmentRef = null
            popupViewRef = null
        }
    }

    /**
     * 检查来电弹窗是否正在显示
     * 用于MessageIncomingManager判断优先级
     */
    fun isShowing(): Boolean {
        return isShowing
    }

    fun enterChannel(): Boolean {
        val callInfo = currentCallInfo ?: return false
        AgodaUtils.joinChannelWithUserAccount(
            callInfo.rtcToken,
            callInfo.channelName!!,
            callInfo.toUserId.toString()
        )
        return true
    }

    fun handleAccept(
        onSuccess: (OnCallMessage) -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        (ActivityUtils.getTopActivity() as? AppCompatActivity)?.let { activity ->
            val callInfo = currentCallInfo ?: return@let

            // 1. 先检查金币是否足够（如果是收费通话）
            if (!callInfo.isFree && !StrategyManager.isReviewPkg()) {
                val enoughCoin = (UserInfoManager.myUserInfo?.availableCoins ?: 0) >= (callInfo.unitPrice ?: 0)
                if (!enoughCoin) {
                    // 金币不足，弹出充值弹窗，不收起来电弹窗，等自动超时
                    (ActivityUtils.getTopActivity() as? FragmentActivity)?.let {

                        // 弹出金币充值弹窗（新版：自动拉取商品数据,显示主播每分钟收费）
                        val dialog = InsufficientBalanceDialog.newInstance(callInfo.unitPrice?: 0)
                        dialog.show(it.supportFragmentManager, "insufficient_balance")

                        ToastUtils.showToast(CallmeApplication.context.getString(R.string.coins_not_enough))
                    } ?: run {
                        hideIncomingCall()
                        cancelAutoHangUpTimer()
                        handleReject(hangUpReason = HangUpReason.NO_COINS)
                        ToastUtils.showToast(CallmeApplication.context.getString(R.string.coins_not_enough))
                    }
                    onError.invoke("User canceled")
                    return@let
                }
            }

            // 2. 金币检查通过后，再请求权限
            AppPermissionManager.checkAndRequestCameraMicrophonePermission(
                activity = activity,
                onGranted = {
                    hideIncomingCall()
                    cancelAutoHangUpTimer()
                    val channelName = callInfo.channelName ?: return@checkAndRequestCameraMicrophonePermission

                    CoroutineScope(Dispatchers.Main).launch {
                        // 获取用户信息
                        UserInfoManager.getUserInfo(callInfo.fromUserId.toString()) { userInfo ->
                            if (userInfo == null) {
                                onError.invoke("User info not available")
                                return@getUserInfo
                            }

                            VideoCallManager.pickUp(
                                scope = this,
                                channelName = channelName,
                                onSuccess = {
                                    enterChannel()
                                    onSuccess.invoke(callInfo)
                                    currentCallInfo = null
                                    // 进入通话页
                                },
                                onError = { errorMsg ->
                                    onError.invoke(errorMsg)
                                    currentCallInfo = null
                                    // 错误处理
                                }
                            )
                        }
                    }
                },
                onDenied = {
                    handleReject()
                }
            )
        }
    }

    /**
     * 来电挂断
     */
    fun handleReject(
        hangUpReason: HangUpReason = HangUpReason.NORMAL,
        onSuccess: () -> Unit = {},
        onError: (String) -> Unit = {}
    ) {
        hideIncomingCall()
        cancelAutoHangUpTimer()
        val callInfo = currentCallInfo ?: return hideIncomingCall()
        val channelName = callInfo.channelName ?: return hideIncomingCall()
        val fromUserId = callInfo.fromUserId ?: ""
        CoroutineScope(Dispatchers.Main).let { scope ->
            VideoCallManager.hangUp(
                scope = scope,
                channelName = channelName,
                hangUpReason = hangUpReason,
                oppositeUserId = fromUserId,
                onSuccess = {
                    onSuccess.invoke()
                },
                onError = { onErrorMsg ->
                    onError.invoke(onErrorMsg)
                }
            )
        }
        currentCallInfo = null
    }

    private fun handleContentClick() {
        val callInfo = currentCallInfo ?: return
        // 优先用 DialogFragment context，否则用 popupView context
        val context = dialogFragmentRef?.get()?.context
            ?: popupViewRef?.get()?.context
            ?: return

        hideIncomingCall()
        VideoCallActivity.startIncoming(context, callInfo)
    }


    private fun playRingtone(context: Context?) {
        if (AppLifecycleManager.isAppInBackground()) {
            return
        }
        context?.let {
            AudioPlayManager.playVideoRingtone(it)
        }
    }

    private fun stopRingtone() {
        ActivityUtils.getTopActivity()?.let {
            AudioPlayManager.stopPlay(it)
        }
    }

    fun shouldShowDialogInActivity(activity: FragmentActivity) {
        // 只在无悬浮窗权限、来电未挂断、当前未弹出DialogFragment时弹出
        if (currentCallInfo != null
            && activity !is VideoCallActivity
            && isTopActivity(activity)
        ) {
            if (!canDrawOverlays(activity)) {
                showDialogFragmentInternal(activity, currentCallInfo!!)
            }
        } else {
            hideIncomingCall()
        }
    }

    private fun canDrawOverlays(context: Context): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            Settings.canDrawOverlays(context.applicationContext)
        } else {
            true
        }
    }

    private fun showDialogFragmentInternal(activity: FragmentActivity, callInfo: OnCallMessage) {
        if (AppLifecycleManager.isAppInBackground()) {
            return
        }
        // 先关闭旧的，再展示新的
        if (dialogFragmentRef != null && dialogFragmentRef!!.get() != null) {
            dialogFragmentRef!!.get()?.dismiss()
        }
        val dialog = CallIncomingDialogFragment.newInstance(callInfo)
        dialog.setOnActionListener(object : CallIncomingDialogFragment.OnActionListener {
            override fun onAccept() {
                handleAccept(
                    onSuccess = { onCallMessage ->
                        VideoCallActivity.startOngoing(
                            activity,
                            onCallMessage.channelName,
                            onCallMessage.toUserId,
                            onCallMessage.fromUserId
                        )
                    }
                )
            }

            override fun onReject() {
                handleReject()
            }

            override fun onContentClick() {
                handleContentClick()
            }
        })
        dialog.show(activity.supportFragmentManager, "call_incoming_dialog")
        dialogFragmentRef = WeakReference(dialog)
        isShowing = true
    }

    private fun startAutoHangUpTimer() {
        autoHangUpJob?.cancel()
        autoHangUpJob = coroutineScope.launch {
            delay(30_000)
            handleReject(HangUpReason.CL_CALL_TIMEOUT)

            // 上报超时挂断日志
            LogReportManager.reportLiveCallEvent(
                action = LiveCallAction.HANGUP,
                ext = LiveCallExt.ON_CALL,
                ext2 = LiveCallExt2.TIME_OUT
            )
        }
    }

    private fun cancelAutoHangUpTimer() {
        autoHangUpJob?.cancel()
        autoHangUpJob = null

        // 停止铃声
        stopRingtone()
    }

    private fun isTopActivity(activity: FragmentActivity): Boolean {
        val am =
            activity.getSystemService(Context.ACTIVITY_SERVICE) as? ActivityManager ?: return true
        val runningTasks = am.getRunningTasks(1)
        if (runningTasks.isNullOrEmpty()) return true
        val topActivity = runningTasks[0].topActivity ?: return true
        return topActivity.className == activity.javaClass.name
    }
} 