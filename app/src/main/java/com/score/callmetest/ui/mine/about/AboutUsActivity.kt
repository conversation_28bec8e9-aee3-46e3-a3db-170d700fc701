package com.score.callmetest.ui.mine.about

import android.os.Build
import android.os.Bundle
import com.score.callmetest.BuildConfig
import com.score.callmetest.databinding.ActivityAboutUsBinding
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.base.EmptyViewModel
import com.score.callmetest.ui.web.WebViewActivity
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.click

class AboutUsActivity : BaseActivity<ActivityAboutUsBinding, EmptyViewModel>() {
    override fun getViewBinding(): ActivityAboutUsBinding {
        return ActivityAboutUsBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = EmptyViewModel::class.java

    override fun initView() {
        // 可在此初始化视图
        binding.tvVer.text = BuildConfig.VERSION_NAME
    }

    override fun initListener() {
        // 返回按钮
        binding.ivBack.click {
            finish()
        }
        // 隐私政策
        binding.itemPrivacyPolicy.click {
            // 跳转到隐私政策页面
            ActivityUtils.startActivity(this@AboutUsActivity, WebViewActivity::class.java,
                Bundle().apply {
                    putString("url", "https://rice.callmeso.com/LinkDuo-Privacy-Policy.html")
                    putString("title", "Privacy Policy")
                }
                ,false)

        }
        // 用户协议
        binding.itemUserAgreement.click {
            // 跳转到用户协议页面
            ActivityUtils.startActivity(this@AboutUsActivity, WebViewActivity::class.java,
                Bundle().apply {
                    putString("url", "https://rice.callmeso.com/LinkDuo-User-Agreement.html")
                    putString("title", "User Agreement")
                }
                ,false)
        }
    }
} 