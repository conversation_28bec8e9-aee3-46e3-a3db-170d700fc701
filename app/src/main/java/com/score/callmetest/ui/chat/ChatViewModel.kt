package com.score.callmetest.ui.chat

import android.net.Uri
import android.os.Handler
import android.os.Looper
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.google.gson.Gson
import com.score.callmetest.entity.ChatMessageEntity
import com.score.callmetest.entity.MessageStatus
import com.score.callmetest.entity.MessageType
import com.score.callmetest.im.RongCloudManager
import com.score.callmetest.ui.message.MessageIncomingManager
import io.rong.imlib.model.Message
import io.rong.message.ImageMessage
import io.rong.message.TextMessage
import kotlinx.coroutines.launch
import timber.log.Timber
import java.util.UUID
import com.score.callmetest.CallStatus
import com.score.callmetest.CallmeApplication
import com.score.callmetest.Constant
import com.score.callmetest.R
import com.score.callmetest.SceneSource
import com.score.callmetest.db.DatabaseFactory
import com.score.callmetest.entity.MessageEvents
import com.score.callmetest.entity.MessageListEntity
import com.score.callmetest.im.callback.ImResultCallback
import com.score.callmetest.im.callback.ImSendMediaMsgCallback
import com.score.callmetest.im.callback.ImSendMsgCallback
import com.score.callmetest.im.entity.HyperLinkMsg
import com.score.callmetest.im.entity.SingleJsonMsg
import com.score.callmetest.manager.AudioPlayManager
import com.score.callmetest.manager.AudioRecorderManager
import com.score.callmetest.manager.FollowManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.manager.UserInfoManager.getUserInfo
import com.score.callmetest.network.AddFriendRequest
import com.score.callmetest.network.BroadcasterModel
import com.score.callmetest.network.ComplainInsertRecordRequest
import com.score.callmetest.network.FAQInfoList
import com.score.callmetest.network.FaqInfo
import com.score.callmetest.network.GetUserListOnlineStatusPostV2Request
import com.score.callmetest.network.GetUserOnlineStatusPostV2Request
import com.score.callmetest.network.GiftInfo
import com.score.callmetest.network.GiveGiftRequest
import com.score.callmetest.network.NetworkResult
import com.score.callmetest.network.RemoveBlockRequest
import com.score.callmetest.network.RetrofitUtils
import com.score.callmetest.network.UserInfo
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.SharePreferenceUtil
import com.score.callmetest.util.TimeUtils
import io.rong.common.FileUtils
import io.rong.message.FileMessage
import io.rong.message.HQVoiceMessage
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Runnable
import kotlinx.coroutines.withContext
import java.util.concurrent.ConcurrentHashMap
import kotlin.collections.contains
import kotlin.collections.set
import kotlin.math.min

/**
 * 聊天界面ViewModel
 */
class ChatViewModel : ViewModel() {

    companion object {
        private const val TAG = "ChatViewModel"
    }

    // 是否是客服
    private var mIsService = true

    // 聊天对象信息
    private val _targetUser = MutableLiveData<UserInfo>()
    val targetUser: LiveData<UserInfo> = _targetUser

    // 消息列表
    private val _messages = MutableLiveData<List<ChatMessageEntity>>(emptyList())
    val messages: LiveData<List<ChatMessageEntity>> = _messages

    // 加载状态
    private val _isLoading = MutableLiveData<Boolean>()
    val mIsLoading: LiveData<Boolean> = _isLoading

    // 发送状态
    private val _sendingStatus = MutableLiveData<SendStatus>()
    val sendingStatus: LiveData<SendStatus> = _sendingStatus

    // 在线状态
    private val _onlineStatus = MutableLiveData<String>()
    val onlineStatus: LiveData<String> = _onlineStatus

    private val handler = Handler(Looper.getMainLooper())
    private val REFRESH_INTERVAL = 10_000L // 10秒
    private val CACHE_VALID_DURATION = 8_000L // 8秒

    private var lastStatusTime = 0L // 上一次更新状态时间
    
    // 相册图片列表
    private val _photoAlbum = MutableLiveData<List<String>>(emptyList())
    val photoAlbum: LiveData<List<String>> = _photoAlbum
    
    // 录音状态
    private val _recordingState = MutableLiveData<RecordingState>()
    val recordingState: LiveData<RecordingState> = _recordingState
    
    // 录音时间文本
    private val _recordTimeText = MutableLiveData(0)
    val recordTimeText: LiveData<Int> = _recordTimeText
    
    // 最大录音时长（ms）
    private val maxRecordingDuration = 60_000L

    // 语音播放暂停
    private val _stopVoice = MutableLiveData<Boolean>()
    val stopVoice: LiveData<Boolean> = _stopVoice

    // 当前用户信息
    private val mCurrentUserId = UserInfoManager.myUserInfo?.userId?:""
    private val mCurrentUserName = UserInfoManager.myUserInfo?.nickname?:""
    private val mCurrentUserAvatar = UserInfoManager.myUserInfo?.avatarThumbUrl?:""

    // 是否开启自动翻译
    private val mIsAutoTrans: Boolean

    /**
     * 用于msg-list
     */
    private var mCurrentItemEntity: MessageListEntity? = null

    init {
        // 设置MessageIncomingManager的聊天消息回调
        MessageIncomingManager.setChatMessageCallback { message ->
            onReceiveMessage(message)
        }

        // 设置录音时间更新监听器
        AudioRecorderManager.setOnRecordingTimeUpdateListener { durationMs ->
            // 更新录音时间文本
            _recordTimeText.postValue((durationMs/1000).toInt())
        }

        // 设置录音错误监听器
        AudioRecorderManager.setOnRecordErrorListener { exception ->
            _recordingState.postValue(RecordingState.ERROR)
        }

        mIsAutoTrans = SharePreferenceUtil.getBoolean(
            Constant.AUTO_TRANSLATE,
            true,
            "settings"
        )
    }

    override fun onCleared() {
        super.onCleared()
        // 清理MessageIncomingManager的聊天消息回调
        MessageIncomingManager.setChatMessageCallback(null)
        
        // 释放音频资源
        AudioRecorderManager.release()

        // handler
        handler.removeCallbacksAndMessages(null)
    }

    /**
     * 初始化聊天界面
     * @param user 目标主播信息
     */
    fun initChat(user: UserInfo) {
        _targetUser.value = user

        // 客服吗
        mIsService = user.userId == StrategyManager.strategyConfig?.userServiceAccountId

        if(!mIsService) {
            // 查询在线状态
            checkOnlineStatus(user.userId)
            // 加载历史消息--客服首次不加载历史，因为没有历史
            _isLoading.value = true
            loadHistoryMessages(user.userId,null)
        }else {
            // 客服加载FAQ信息
            loadFaqs()
        }
    }

    // <editor-folder desc="加载faq-list">

    private var mFaqInfoResponse: FAQInfoList? = null

    private val mSysServiceName by lazy {
        CallmeApplication.context.getString(R.string.msg_item_service_name)
    }

    private fun loadFaqs() {
        viewModelScope.launch {
            try {
                val response = withContext(Dispatchers.IO) {
                    RetrofitUtils.dataRepository.getFaqList()
                }
                if (response is NetworkResult.Success) {
                    mFaqInfoResponse = response.data
                    if(mFaqInfoResponse == null) return@launch
                    // 拿到信息了
                    makeFaqChatMsgs(mFaqInfoResponse!!)
                }
            } catch (e: Exception) {
                Timber.e(e)
            }
        }
    }

    /**
     * 制作常见问题聊天消息
     * @param [mFaqInfoResponse] FAQ信息回复
     */
    private fun makeFaqChatMsgs(mFaqInfoResponse: FAQInfoList) {
        val newMessages = _messages.value?.toMutableList() ?: mutableListOf()

        // 构建FAQ消息内容：content + 问题列表
        val content = StringBuilder(mFaqInfoResponse.content)
        mFaqInfoResponse.faqInfoList?.forEach { faqInfo ->
            // todo-dsc 暂时先过滤掉没有回复的questions
            if(faqInfo.messageAnswer?.content != null) {
                content.append("\n\n").append(faqInfo.question)
            }
        }

        // 将FAQ信息序列化为JSON存储在extra字段中
        val faqInfoJson = try {
            Gson().toJson(mFaqInfoResponse)
        } catch (e: Exception) {
            Timber.e(e, "Failed to serialize FAQ info")
            ""
        }

        // 创建FAQ消息
        val faqMessage = ChatMessageEntity(
            messageId = UUID.randomUUID().toString(),
            senderId = StrategyManager.strategyConfig?.userServiceAccountId ?: "",
            senderName = mSysServiceName,
            senderAvatar = _targetUser.value?.avatarThumbUrl ?: "",
            receiverId = mCurrentUserId,
            content = content.toString(),
            messageType = MessageType.SYSTEM,
            status = MessageStatus.RECEIVED,
            timestamp = System.currentTimeMillis(),
            isCurrentUser = false,
            extra = faqInfoJson
        )

        newMessages.add(faqMessage)
        _messages.postValue(newMessages)
    }

    /**
     * 处理FAQ问题点击事件
     * @param faqCode FAQ问题的code
     */
    fun handleFaqQuestionClick(faqCode: Int) {
        viewModelScope.launch {
            val faqInfo = mFaqInfoResponse?.faqInfoList?.find { it.code == faqCode }
            if (faqInfo != null) {
                Timber.d("FAQ question clicked: code=$faqCode, question=${faqInfo.question}")

                // 1. 用户发送问题文本消息
                sendUserQuestionMessage(faqInfo.question)

                // 2. 客服自动回复答案（如果有messageAnswer）
                if (faqInfo.messageAnswer != null) {
                    sendFaqAnswerMessage(faqInfo)
                }
            } else {
                Timber.w("FAQ info not found for code: $faqCode")
            }
        }
    }

    /**
     * 发送用户问题消息
     * @param question 问题文本
     */
    private fun sendUserQuestionMessage(question: String) {
        // 过滤一下开头 *
        val questionText = question.trimStart('*')
        // 发送普通文本消息
        sendTextMessage(questionText.trim())
    }

    /**
     * 发送FAQ回答消息（延迟一点时间模拟客服回复）
     * @param faqInfo FAQ信息
     */
    private fun sendFaqAnswerMessage(faqInfo: FaqInfo) {
        val answerContent = faqInfo.messageAnswer?.content ?: return
        // 创建普通文本回答消息
        val answerMessage = ChatMessageEntity(
            messageId = UUID.randomUUID().toString(),
            senderId = StrategyManager.strategyConfig?.userServiceAccountId ?: "",
            senderName = mSysServiceName,
            senderAvatar = _targetUser.value?.avatarThumbUrl ?: "",
            receiverId = mCurrentUserId,
            content = answerContent,
            messageType = MessageType.SYSTEM,
            status = MessageStatus.RECEIVED,
            timestamp = System.currentTimeMillis(),
            isCurrentUser = false,
            extra = null
        )

        // 添加到消息列表
        val currentMessages = _messages.value?.toMutableList() ?: mutableListOf()
        currentMessages.add(answerMessage)
        _messages.postValue(currentMessages)
    }

    // </editor-folder>

    // <editor-folder desc="更新在线状态">

    /**
     * 查询在线状态
     */
    fun checkOnlineStatus(userId: String?){
        if(userId.isNullOrEmpty()) return
        val currentTime = System.currentTimeMillis()
        val toRefresh = currentTime - lastStatusTime >= CACHE_VALID_DURATION
        if (!toRefresh) {
            // CACHE_VALID_DURATION时间内不更新状态
            return
        }
        viewModelScope.launch {
            try {
                // 适配审核模式
                if (StrategyManager.isReviewPkg()) {
                    getUserInfo(userId) { userInfo ->
                        val status = if (userInfo?.isAnswer == true && !StrategyManager.reviewPkgUsers.contains(userInfo.userId)) {
                            CallStatus.ONLINE
                        }else {
                            GlobalManager.getReviewOtherStatus(userInfo?.userId)
                        }
                        _onlineStatus.postValue(status)
                        lastStatusTime = System.currentTimeMillis()
                    }
                    return@launch
                }

                val response = withContext(Dispatchers.IO) {
                    val request = GetUserOnlineStatusPostV2Request(userId)
                    RetrofitUtils.dataRepository.getUserOnlineStatusPostV2(request)
                }

                if(response is NetworkResult.Success){
                    var status = response.data?: CallStatus.UNKNOWN

                    // 适配审核模式
                    if (StrategyManager.isReviewPkg()) {
                        getUserInfo(userId) { userInfo ->
                            if (userInfo?.isAnswer == true && !StrategyManager.reviewPkgUsers.contains(userInfo.userId)) {
                                status = CallStatus.ONLINE
                            }else {
                                status = GlobalManager.getReviewOtherStatus(userInfo?.userId)
                            }
                        }
                    }

                    UserInfoManager.putCachedStatus(userId, status)

                    _onlineStatus.postValue(status)
                    lastStatusTime = System.currentTimeMillis()
                }
            } catch (e: Exception) {
                Timber.e(e)
            }
        }
    }

    /**
     * 10秒定时刷新（Fragment可在onResume注册，onPause注销）
     */
    private var isTimerActive = false
    private var onPeriodicRefresh: (() -> Unit)? = null
    private val periodicRefreshRunnable = object : Runnable {
        override fun run() {
            if (isTimerActive) {
                onPeriodicRefresh?.invoke()
                handler.postDelayed(this, REFRESH_INTERVAL)
            }
        }
    }
    fun startStatusUpdates(refreshCallback: () -> Unit) {
        onPeriodicRefresh = refreshCallback
        isTimerActive = true
        handler.removeCallbacks(periodicRefreshRunnable)
        // 首次立即刷新
        refreshCallback()
        // 10秒后开始定时刷新
        handler.postDelayed(periodicRefreshRunnable, REFRESH_INTERVAL)
    }

    fun stopStatusUpdates() {
        isTimerActive = false
        handler.removeCallbacks(periodicRefreshRunnable)
        onPeriodicRefresh = null
    }

    // </editor-folder>

    /**
     * 加载历史消息
     * @param targetUserId 目标用户ID
     * @param oldestMessageId 最后一条消息的 ID。如需查询本地数据库中最新的消息，设置为 -1
     */
    fun loadHistoryMessages(targetUserId: String?, oldestMessageId: Int? = -1 ) {
        Timber.tag(RongCloudManager.LOG_TAG).d("loadHistoryMessages: $oldestMessageId")

        if(mIsService) {
            _isLoading.value = false
            return
        }

        val rcOldMsgId = if(oldestMessageId == -2){
            -1
        }else oldestMessageId

        if(targetUserId.isNullOrEmpty()) return
        viewModelScope.launch(Dispatchers.IO) {
            // 使用融云SDK加载历史消息
            RongCloudManager.getLocalHistoryMessages(
                targetUserId,
                rcOldMsgId?:-1,
                10,
                object : ImResultCallback<List<Message>>() {

                    override fun error(code: Int?, errorMsg: String?) {
                        Timber.tag(RongCloudManager.LOG_TAG).e("获取历史消息失败: $code---$errorMsg")
                        // 加载失败
                        _isLoading.postValue(false)
                    }

                    override fun success(t: List<Message>?) {
                        // 查询成功
                        Timber.tag(RongCloudManager.LOG_TAG).d("获取历史消息成功: ${t?.size}")
                        val chatMessages = t?.filter {
                            // todo dsc 目前先过滤一下消息，只接收指定消息 -- 1.0版本不做
                            return@filter when(it.content){
                                is TextMessage -> true
                                is ImageMessage -> true
                                is HQVoiceMessage -> true
                                is SingleJsonMsg -> true
                                else -> false
                            }
                        }?.map { convertRongMessageToChatMessage(it) } ?: return

                        // 新查询出来的数据在前面，老数据在后面
                        val newMessages = if(_messages.value?.isEmpty() ?: true || oldestMessageId == -2){
                            chatMessages.reversed()
                        }else {
                            chatMessages.reversed() + (_messages.value as List<ChatMessageEntity>)
                        }

                        _messages.postValue(newMessages)

                        // 只在首次load的时候更新
                        if(oldestMessageId == null && newMessages.isNotEmpty()) {
                            saveLastMessage(newMessages.last())
                        }
                    }
                }
            )
        }
    }

    // <editor-folder desc="关注、拉黑、举报">

    // 关注 - 通过FollowManager
    fun follow(userId: String, callback: (Boolean) -> Unit) {
        FollowManager.followUser(userId, object : FollowManager.FollowActionCallback {
            override fun onSuccess() {
                val targetUser = _targetUser.value!!.apply {
                    isFriend = true
                }
                _targetUser.postValue(targetUser)
                callback(true)
            }

            override fun onError(errorMsg: String) {
                callback(false)
            }
        })
    }

    // 取关 - 通过FollowManager
    fun unfollow(userId: String, callback: (Boolean) -> Unit) {
        FollowManager.unfollowUser(userId, object : FollowManager.FollowActionCallback {
            override fun onSuccess() {
                val targetUser = _targetUser.value!!.apply {
                    isFriend = false
                }
                _targetUser.postValue(targetUser)
                callback(true)
            }

            override fun onError(errorMsg: String) {
                callback(false)
            }
        })
    }

    // 拉黑
    fun block(userId: String, callback: (Boolean) -> Unit) {
        viewModelScope.launch {
            try {
                val resp = withContext(Dispatchers.IO) {
                    RetrofitUtils.dataRepository.insertComplainRecord(
                        ComplainInsertRecordRequest(
                            broadcasterId = userId,
                            channelName = null,
                            complainCategory = "Block",
                            complainSub = null,
                            isAudit = false,
                            reason = null,
                            snapshotPath = null
                        )
                    )
                }
                if(resp is NetworkResult.Success){
                    val targetUser = _targetUser.value!!.apply {
                        isBlock = true
                    }
                    _targetUser.postValue(targetUser)
                    callback(true)
                }else {
                    callback(false)
                }
            } catch (e: Exception) {
                callback(false)
            }
        }
    }

    // 取消拉黑
    fun unblock(userId: String, callback: (Boolean) -> Unit) {
        viewModelScope.launch {
            try {
                val resp = withContext(Dispatchers.IO) {
                    RetrofitUtils.dataRepository.removeBlock(
                        RemoveBlockRequest(blockUserId = userId)
                    )
                }
                if (resp is NetworkResult.Success) {
                    val targetUser = _targetUser.value!!.apply {
                        isBlock = false
                    }
                    _targetUser.postValue(targetUser)
                    callback(true)
                } else {
                    callback(false)
                }
            } catch (e: Exception) {
                callback(false)
            }
        }
    }

    // 举报（带 complainSub）
    fun reportUser(userId: String, category: String, complainSub: String?, callback: (Boolean) -> Unit) {
        viewModelScope.launch {
            try {
                val resp = withContext(Dispatchers.IO) {
                    RetrofitUtils.dataRepository.insertComplainRecord(
                        ComplainInsertRecordRequest(
                            broadcasterId = userId,
                            channelName = null,
                            complainCategory = category,
                            complainSub = complainSub,
                            isAudit = false,
                            reason = null,
                            snapshotPath = null
                        )
                    )
                }
                callback(resp is NetworkResult.Success)
            } catch (e: Exception) {
                callback(false)
            }
        }
    }

    // </editor-folder>

    // <editor-folder desc="发送消息相关">

    /**
     * 发送文本消息
     * @param content 消息内容
     */
    fun sendTextMessage(content: String,resendMessage: ChatMessageEntity? = null) {
        if (content.isBlank()) return
        val targetUserId = _targetUser.value?.userId ?: return

        // 创建新消息
        val newMessage = resendMessage ?: ChatMessageEntity(
            messageId = UUID.randomUUID().toString(),
            senderId = mCurrentUserId,
            senderName = mCurrentUserName,
            senderAvatar = mCurrentUserAvatar,
            receiverId = targetUserId,
            content = content,
            messageType = MessageType.TEXT,
            status = MessageStatus.SENDING,
            timestamp = System.currentTimeMillis(),
            isCurrentUser = true
        )

        val messageId = newMessage.messageId

        // 添加到消息列表
        val currentMessages = _messages.value?.toMutableList() ?: mutableListOf()
        if(resendMessage != null){
            // 重发--更新发送时间、状态
            val deleteMessage = currentMessages.find { msg ->
                msg.messageId == newMessage.messageId
            }
            currentMessages.remove(deleteMessage)
            newMessage.apply {
                this.status = MessageStatus.SENDING
                this.timestamp = System.currentTimeMillis()
            }
        }
        currentMessages.add(newMessage)
        _messages.value = currentMessages

        // msg-list-save
        saveLastMessage(newMessage)

        // 客服不走融云
        if(mIsService){
            updateMessageStatus(messageId, MessageStatus.SENT)
            _sendingStatus.postValue(SendStatus.Success(messageId))
            return
        }

        viewModelScope.launch(Dispatchers.IO) {
            // 使用融云SDK发送消息
            RongCloudManager.sendTextMessage(
                targetUserId,
                content,
                callback = object : ImSendMsgCallback() {

                    override fun success(message: Message?) {
                        // 消息发送成功
                        Timber.tag("RongCloud").d("消息发送成功--${message?.content}")
                        updateMessageStatus(messageId, MessageStatus.SENT)
                        _sendingStatus.postValue(SendStatus.Success(messageId))
                    }

                    override fun failed(code: Int?, errorMsg: String?) {
                        // 消息发送失败
                        updateMessageStatus(messageId, MessageStatus.FAILED)
                        _sendingStatus.postValue(SendStatus.Error(messageId, "发送失败: $errorMsg"))
                    }
                }
            )
        }
    }

    /**
     * 发送图片消息
     * @param imageUri 图片URL
     */
    fun sendImageMessage(imageUri: Uri,resendMessage: ChatMessageEntity? = null) {
        val targetUserId = _targetUser.value?.userId ?: return

        // 创建新消息
        val newMessage = resendMessage ?: ChatMessageEntity(
            messageId = UUID.randomUUID().toString(),
            senderId = mCurrentUserId,
            senderName = mCurrentUserName,
            senderAvatar = mCurrentUserAvatar,
            receiverId = targetUserId,
            content = "",
            messageType = MessageType.IMAGE,
            status = MessageStatus.SENDING,
            timestamp = System.currentTimeMillis(),
            isCurrentUser = true,
            mediaLocalUri = imageUri
        )
        val messageId = newMessage.messageId


        // 添加到消息列表
        val currentMessages = _messages.value?.toMutableList() ?: mutableListOf()
        if(resendMessage != null){
            // 重发--更新发送时间、状态
            val deleteMessage = currentMessages.find { msg ->
                msg.messageId == newMessage.messageId
            }
            currentMessages.remove(deleteMessage)
            newMessage.apply {
                this.status = MessageStatus.SENDING
                this.timestamp = System.currentTimeMillis()
            }
        }
        currentMessages.add(newMessage)
        _messages.value = currentMessages

        // msg-list-save
        saveLastMessage(newMessage)

        viewModelScope.launch(Dispatchers.IO) {
            // 使用融云SDK发送图片消息
            try {
                RongCloudManager.sendImageMessage(
                    targetUserId,
                    imageUri,
                    callback = object : ImSendMediaMsgCallback() {

                        override fun success(message: Message?) {
                            updateMessageStatus(messageId, MessageStatus.SENT)
                            _sendingStatus.postValue(SendStatus.Success(messageId))
                        }

                        override fun faild(code: Int?, errorMsg: String?) {
                            updateMessageStatus(messageId, MessageStatus.FAILED)
                            _sendingStatus.postValue(
                                SendStatus.Error(
                                    messageId,
                                    "发送失败: $errorMsg"
                                )
                            )
                        }

                        override fun onProgress(message: Message?, progress: Int) {
                            // 上传进度
                            Timber.tag("RongCloud").d("图片上传进度: $progress")
                        }
                    }
                )
            } catch (e: Exception) {
                Timber.tag("RongCloud").e("发送图片消息失败: ${e.message}")
                updateMessageStatus(messageId, MessageStatus.FAILED)
                _sendingStatus.postValue(SendStatus.Error(messageId, "发送失败: ${e.message}"))
            }
        }
    }
    
    /**
     * 发送语音消息
     * @param duration 语音时长（ms）
     * @param audioUri 语音文件路径
     */
    fun sendVoiceMessage(duration: Long, audioUri: Uri?,resendMessage: ChatMessageEntity? = null) {
        val targetUserId = _targetUser.value?.userId ?: return
        if(audioUri == null) return

        // 创建新消息
        val newMessage = resendMessage ?: ChatMessageEntity(
            messageId = UUID.randomUUID().toString(),
            senderId = mCurrentUserId,
            senderName = mCurrentUserName,
            senderAvatar = mCurrentUserAvatar,
            receiverId = targetUserId,
            content = "",
            messageType = MessageType.VOICE,
            status = MessageStatus.SENDING,
            timestamp = System.currentTimeMillis(),
            isCurrentUser = true,
            mediaLocalUri = audioUri,
            mediaDuration = duration
        )
        val messageId = newMessage.messageId

        // 添加到消息列表
        val currentMessages = _messages.value?.toMutableList() ?: mutableListOf()
        if(resendMessage != null){
            // 重发--更新发送时间、状态
            val deleteMessage = currentMessages.find { msg ->
                msg.messageId == newMessage.messageId
            }
            currentMessages.remove(deleteMessage)
            newMessage.apply {
                this.status = MessageStatus.SENDING
                this.timestamp = System.currentTimeMillis()
            }
        }
        currentMessages.add(newMessage)
        _messages.value = currentMessages

        // msg-list-save
        saveLastMessage(newMessage)

        viewModelScope.launch(Dispatchers.IO) {
            // 使用融云SDK发送图片消息
            try {
                RongCloudManager.sendVoiceMessage(
                    targetUserId,
                    audioUri,
                    (min(duration, maxRecordingDuration) /1000).toInt(),
                    callback = object : ImSendMediaMsgCallback() {

                        override fun success(message: Message?) {
                            updateMessageStatus(messageId, MessageStatus.SENT)
                            _sendingStatus.postValue(SendStatus.Success(messageId))
                        }

                        override fun faild(code: Int?, errorMsg: String?) {
                            updateMessageStatus(messageId, MessageStatus.FAILED)
                            _sendingStatus.postValue(
                                SendStatus.Error(
                                    messageId,
                                    "发送失败: $errorMsg"
                                )
                            )
                        }

                        override fun onProgress(message: Message?, progress: Int) {
                            // 上传进度
                            Timber.tag("RongCloud").d("语音上传进度: $progress")
                        }
                    }
                )
            } catch (e: Exception) {
                Timber.tag("RongCloud").e("发送语音消息失败: ${e.message}")
                updateMessageStatus(messageId, MessageStatus.FAILED)
                _sendingStatus.postValue(SendStatus.Error(messageId, "发送失败: ${e.message}"))
            }
        }
    }

    /**
     * 送礼物
     * @param [giftInfo] 礼物信息
     * @param [resendMessage] 重新发送信息
     */
    fun sendGift(giftInfo: GiftInfo?,resendMessage: ChatMessageEntity? = null){
        Timber.d("receive send gift cmd---$giftInfo")
        if(giftInfo == null) return
        // 先确定targetUser是否还在
        val targetUserId = _targetUser.value?.userId ?: return
        // 1. 中chat页面展示发送中。。。
        val newMessage = makeGiftMessageEntity(giftInfo,resendMessage)
        // 2. 请求后台发送礼物
        viewModelScope.launch {
            try {
                val response = withContext(Dispatchers.IO) {
                    val request = GiveGiftRequest(giftCode = giftInfo.code, num = 1, recipientUserId = targetUserId,
                        sceneSource = SceneSource.IM_GIFT_PANEL, giftSource = 0)
                    RetrofitUtils.dataRepository.giveUserGifts(request)
                }

                Timber.d("gift-sent-$response")
                if (response is NetworkResult.Success) {
                    // 赠送成功--发送融云gift消息并更新UI
                    sendGiftMessage(giftInfo.code?: "",giftInfo, success = {
                        updateMessageStatus(newMessage.messageId, MessageStatus.SENT)
                        _sendingStatus.postValue(SendStatus.Success(newMessage.messageId))
                    }, failed = { _, errorMsg ->
                        updateMessageStatus(newMessage.messageId, MessageStatus.FAILED)
                        _sendingStatus.postValue(SendStatus.Error(newMessage.messageId, "发送失败: $errorMsg"))
                    })
                }else {
                    // 赠送失败-也标记失败，但不记录数据库
                    updateMessageStatus(newMessage.messageId, MessageStatus.FAILED)
                    _sendingStatus.postValue(SendStatus.Error(newMessage.messageId, "接口请求发送失败.."))
                }
            } catch (e: Exception) {
                Timber.e(e)
            }
        }

    }

    /**
     * make gift信息实体--用于展示
     *
     * 若10s内多次送出同一个礼物，gift信息需要合并，后面显示 x1、x2、x3...
     * 合并逻辑： 没有送出成功的，不合并；合并的以这里make时候的sentTime为主
     *
     * ps. 和UI确认，不需要x1,x2,x3...只需要x1。
     *
     * @param [giftInfo] 礼物信息
     */
    private fun makeGiftMessageEntity(giftInfo: GiftInfo,resendMessage: ChatMessageEntity? = null): ChatMessageEntity{
        // 创建新消息
        val newMessage = resendMessage ?: ChatMessageEntity(
            messageId = UUID.randomUUID().toString(),
            senderId = mCurrentUserId,
            senderName = mCurrentUserName,
            senderAvatar = mCurrentUserAvatar,
            receiverId = _targetUser.value?.userId?:"",
            content = "",
            messageType = MessageType.GIFT,
            status = MessageStatus.SENDING,
            timestamp = System.currentTimeMillis(),
            isCurrentUser = true,
            giftInfo = giftInfo
        )

        // 添加到消息列表
        val currentMessages = _messages.value?.toMutableList() ?: mutableListOf()
        if(resendMessage != null){
            val deleteMessage = currentMessages.find { msg ->
                msg.messageId == newMessage.messageId
            }
            currentMessages.remove(deleteMessage)
            newMessage.apply {
                this.status = MessageStatus.SENDING
                this.timestamp = System.currentTimeMillis()
            }
        }
        currentMessages.add(newMessage)
        _messages.value = currentMessages

        // msg-list-save
        saveLastMessage(newMessage)

        return newMessage
    }
    
    /**
     * 发送礼物消息
     * @param giftId 礼物ID
     * @param gift 礼物信息
     */
    private fun sendGiftMessage(giftId: String,gift: GiftInfo, success:() -> Unit, failed: (Int?, String?) -> Unit) {
        val targetUserId = _targetUser.value?.userId ?: return

        viewModelScope.launch(Dispatchers.IO) {
            // 使用融云SDK发送礼物消息
            try {
                RongCloudManager.sendGiftMessage(
                    targetUserId,
                    giftId,
                    _targetUser.value?.nickname?:"",
                    mCurrentUserName,
                    gift,
                    callback = object : ImSendMsgCallback() {
                        override fun success(message: Message?) {
                            success()
                        }

                        override fun failed(code: Int?, errorMsg: String?) {
                            failed(code,errorMsg)
                        }
                    }
                )
            } catch (e: Exception) {
                Timber.tag("RongCloud").e("发送gift消息失败: ${e.message}")
                failed(null,e.message)
            }
        }

    }

    /**
     * 重新发送失败的消息
     * @param message 要重发的消息
     */
    fun resendMessage(message: ChatMessageEntity) {
        // 先删除这套消息，重新发送内容相同的新消息
        try {
            // 看转Int是否报错
            val msgId = message.messageId.toInt()
            // 说明是从融云本地数据库读取的，可以转Int
            deleteRcLocalMessage(msgId)
        }catch (e: Exception){
            // 转Int报错---说明messageId存的是UUID--说明此条消息没有存在融云本地数据库
            // 那就-直接remove从list中就ok来
            // 此处不需要操作，sent中有remove操作
        }

        // 根据消息类型重新发送
        when (message.messageType) {
            MessageType.TEXT -> {
                sendTextMessage(message.content,message)
            }
            MessageType.IMAGE -> {
                message.mediaLocalUri?.let { sendImageMessage(it,message) }
            }
            MessageType.VOICE -> {
                sendVoiceMessage(message.mediaDuration, message.mediaLocalUri,message)
            }
            MessageType.GIFT -> {
               sendGift(message.giftInfo,message)
            }
            else -> {
                // 其他类型消息暂不处理
                Timber.tag("RongCloud").e("不支持的消息类型: ${message.messageType}")
                updateMessageStatus(message.messageId, MessageStatus.FAILED)
                _sendingStatus.postValue(SendStatus.Error(message.messageId, "不支持的消息类型"))
            }
        }
    }

    // </editor-folder>

    /**
     * 删除融云本地
     */
    private fun deleteRcLocalMessage(vararg ids: Int){
        viewModelScope.launch(Dispatchers.IO) {
            try{
                RongCloudManager.deleteMessages(*ids, callback = object: ImResultCallback<Boolean>(){
                    override fun success(t: Boolean?) {
                        Timber.d("delete message ok...$ids")
                    }

                    override fun error(code: Int?, errorMsg: String?) {
                        Timber.d("delete message failed...$errorMsg")
                    }
                })
            }catch (e: Exception) {
                Timber.e(e)
            }
        }
    }

    /**
     * c
     * @param user 目标用户
     */
    fun startVideoCall(user: BroadcasterModel) {
        Timber.tag("VideoCall").d("发起视频通话: ${user.userId}")
        // startVideoCall
    }

    /**
     * 更新消息状态
     * @param messageId 消息ID
     * @param status 新状态
     */
    private fun updateMessageStatus(messageId: String, status: MessageStatus) {
        val currentMessages = _messages.value?.toMutableList() ?: return
        val index = currentMessages.indexOfFirst { it.messageId == messageId }
        
        if (index != -1) {
            val updatedMessage = currentMessages[index].withStatus(status)
            currentMessages[index] = updatedMessage
            if(Looper.myLooper() == Looper.getMainLooper()) {
                // 主线程--直接set
                _messages.value = currentMessages
            }else {
                _messages.postValue(currentMessages)
            }
        }
    }

    
    /**
     * 消息发送状态
     */
    sealed class SendStatus {
        data class Success(val messageId: String) : SendStatus()
        data class Error(val messageId: String, val errorMessage: String) : SendStatus()
    }

    // <editor-folder desc="语音消息相关" >

    /**
     * 录音状态枚举
     */
    enum class RecordingState {
        INITIALIZED,    // 初始化完成
        RECORDING,      // 录音中
        COMPLETED,      // 录音完成
        COMPLETED_1S,   // 录音完成但小于1s
        CANCELED,       // 录音取消
        ERROR           // 录音错误
    }
    
    /**
     * 开始录音
     */
    fun startRecording() {
        // 设置录音状态为正在录音
        _recordingState.value = RecordingState.RECORDING
        
        // 使用AudioManager开始录音
        val success = AudioRecorderManager.startRecording(
            CallmeApplication.context,
            maxRecordingDuration
        ){
            // 达到最大录音时长了，不需要等用户触发stop，自行触发
            stopRecordingAndSend()
        }
        
        if (!success) {
            _recordingState.value = RecordingState.ERROR
        }
    }
    
    /**
     * 停止录音并发送
     */
    fun stopRecordingAndSend() {
        // 没有开始录音--stop也没意义
        if(_recordingState.value != RecordingState.RECORDING) return
        // 使用AudioManager停止录音
        val result = AudioRecorderManager.stopRecording()
        
        if (result == null) {
            _recordingState.value = RecordingState.ERROR
            return
        }
        
        val (fileUri, duration) = result
        
        // 检查录音时长
        if (duration < 1000L) {
            // 小于1s
            _recordingState.value = RecordingState.COMPLETED_1S
            return
        }
        
        // 设置录音状态为完成
        _recordingState.value = RecordingState.COMPLETED
        
        // 发送语音消息
        sendVoiceMessage(duration, fileUri)
    }
    
    /**
     * 取消录音
     */
    fun cancelRecording() {
        // 使用AudioManager取消录音
        AudioRecorderManager.cancelRecording()
        
        // 设置录音状态为取消
        _recordingState.value = RecordingState.CANCELED
    }
    
    /**
     * 获取录音剩余时间
     */
    fun getRecordingRemainingTime(): Long {
        return maxRecordingDuration - AudioRecorderManager.getCurrentRecordingDuration()
    }

    /**
     * 语音下载完成更新
     */
    fun updateAudioDownload(event: MessageEvents){
        viewModelScope.launch(Dispatchers.IO) {
            // 消息列表中查询这条语音并更新
            val msgId = (event as MessageEvents.AudioDownloadOk).msgId
            val localUri = event.localUri
            val newList = _messages.value?.map { ms ->
                if(ms.messageId == msgId){
                    ms.copy(mediaLocalUri = localUri, isPlaying = false, isChange = true)
                } else ms
            }
            newList?.takeIf { it !== _messages.value }?.let { nonNullList ->
                _messages.postValue(nonNullList)
            }
        }
    }

    /**
     * 播放or 下载并播放 语音消息
     *
     * @param messageEntity 当前语音Item信息-自己
     *
     */
    fun handleVoiceClick(messageEntity: ChatMessageEntity) {
        viewModelScope.launch {
            // 处理暂停逻辑
            if (AudioPlayManager.isPlaying()) {
                val playingUri = AudioPlayManager.getPlayingUri()
                _stopVoice.value = true
                if(playingUri == messageEntity.mediaLocalUri){
                    // 暂停的是当前播放的 Uri
                    return@launch
                }
            }
            // 下载-播放处理
            playOrDownloadHQVoiceMsg(messageEntity)
        }
    }


    /**
     * 播放或下载
     *
     * @param [messageEntity] 消息实体
     */
    private fun playOrDownloadHQVoiceMsg(messageEntity: ChatMessageEntity){
        val uriValid = messageEntity.mediaLocalUri?.toString()?.isBlank()?:false
        val isNeedDownload = uriValid || !FileUtils.isFileExistsWithUri(CallmeApplication.context,messageEntity.mediaLocalUri)
        if(isNeedDownload) {
            // download--播放
        }else {
            // 播放
            playVoiceMessage(messageEntity)
        }
    }

    /**
     * 播放语音
     * @param [messageEntity] 消息实体
     */
    private fun playVoiceMessage(messageEntity: ChatMessageEntity){
        AudioPlayManager.startPlay(
            CallmeApplication.context,
            messageEntity.mediaLocalUri,
            object: AudioPlayManager.IAudioPlayListener{
                override fun onStart(uri: Uri?) {
                    messageEntity.isPlaying = true
                    refreshSingleMessage(messageEntity)
                }

                override fun onStop(uri: Uri?) {
                    messageEntity.isPlaying = false
                    refreshSingleMessage(messageEntity)
                }

                override fun onComplete(uri: Uri?) {
                    messageEntity.isPlaying = false
                    refreshSingleMessage(messageEntity)
                    // todo-dsc 继续找下一个语音消息播放--1.0版本不做
                }
            }
        )
    }

    /**
     * 刷新单个消息
     * @param [messageEntity] 消息实体
     */
    private fun refreshSingleMessage(messageEntity: ChatMessageEntity) {
        val newList = _messages.value?.map { ms ->
            if(ms.messageId == messageEntity.messageId){
                if(ms !== messageEntity){
                    messageEntity.isChange = true
                    return@map messageEntity
                }
                ms.copy(isChange = true)
            } else ms
        }
        newList?.takeIf { it !== _messages.value }?.let { nonNullList ->
            _messages.postValue(nonNullList)
        }
    }


    // </editor-folder>
    
    /**
     * 处理从MessageIncomingManager接收到的聊天消息
     * 替代原来的融云消息监听
     */
    private fun onReceiveMessage(message: Message) {
        Timber.tag(TAG).d("Received chat message from MessageIncomingManager: ${message.senderUserId}")

        // MessageIncomingManager已经过滤了消息类型和聊天对象，这里直接处理
        val chatMessage = convertRongMessageToChatMessage(message)

        // 添加到消息列表
        val currentMessages = _messages.value?.toMutableList() ?: mutableListOf()
        currentMessages.add(chatMessage)
        _messages.postValue(currentMessages)
    }
    
    /**
     * 将融云消息转换为应用内消息格式
     */
    private fun convertRongMessageToChatMessage(message: Message): ChatMessageEntity {
        val isCurrentUser = message.senderUserId == mCurrentUserId
        val messageContent = message.content
        // 文本
        val content = when (messageContent) {
            is TextMessage -> messageContent.content
            is HyperLinkMsg -> messageContent.content
            is SingleJsonMsg -> messageContent.content
            else -> null
        }?:""
        // type
        val messageType = when (messageContent) {
            is TextMessage -> MessageType.TEXT
            is ImageMessage -> MessageType.IMAGE
            is HQVoiceMessage -> MessageType.VOICE
            is HyperLinkMsg -> MessageType.LINK
            is SingleJsonMsg -> MessageType.GIFT
            is FileMessage -> MessageType.FILE
//            is D -> MessageType.GIFT // 礼物消息还没自定义
            else -> MessageType.TEXT
        }
        // thumbUri
        val thumbUri = if(messageContent is ImageMessage) messageContent.thumUri else null

        // uri-如果有
        val mediaUri = when(messageContent){
            is ImageMessage -> messageContent.mediaUrl
            is HQVoiceMessage -> messageContent.mediaUrl
            else -> null
        }

        // local-uri-如果有
        val localMediaUri = when(messageContent){
            is ImageMessage -> messageContent.localPath
            is HQVoiceMessage -> messageContent.localPath
            else -> null
        }

        // duration-如果有
        val duration = when(messageContent){
            is HQVoiceMessage -> messageContent.duration * 1000L
            else -> 0
        }


        // giftInfo-如果有
        val gift = when(messageContent){
            is SingleJsonMsg -> {
                try {
                    Gson().fromJson(messageContent.localExtra, GiftInfo::class.java)
                } catch (e: Exception) {
                    null
                }
            }
            else -> null
        }

        // extra-如果有
        val extra = when(messageContent){
            is HyperLinkMsg -> messageContent.localExtra
            is SingleJsonMsg -> messageContent.localExtra
//            is NoneFlagMsg -> messageContent.localPath
            else -> null
        }?:""

        // status
        val status = when(message.sentStatus) {
            Message.SentStatus.SENDING -> MessageStatus.SENDING
            Message.SentStatus.SENT -> MessageStatus.SENT
            Message.SentStatus.FAILED -> MessageStatus.FAILED
            Message.SentStatus.RECEIVED -> MessageStatus.RECEIVED
            Message.SentStatus.READ -> MessageStatus.READ
            else -> {MessageStatus.SENT}
        }

        return ChatMessageEntity(
            messageId = message.messageId.toString(),
            senderId = message.senderUserId,
            senderName =  if (isCurrentUser) mCurrentUserName else _targetUser.value?.nickname?:"",
            senderAvatar = if (isCurrentUser) mCurrentUserAvatar else _targetUser.value?.avatarThumbUrl?:"",
            receiverId = message.targetId,
            content = content,
            messageType = messageType,
            status = status,
            timestamp = message.sentTime,
            isCurrentUser = isCurrentUser,
            thumbUri = thumbUri,
            mediaUri = mediaUri,
            mediaLocalUri = localMediaUri,
            mediaDuration = duration,
            giftInfo = gift,
            isAutoTrans = mIsAutoTrans,
            extra = extra
        )
    }

    /**
     * 加载相册图片
     */
    fun loadPhotoAlbum() {
        viewModelScope.launch {

            val userInfo = _targetUser.value
            if(userInfo == null) return@launch

            // 相册图片数据
            val photos = userInfo.mediaList?.mapNotNull { it.mediaUrl }?:emptyList()
            
            _photoAlbum.postValue(photos)
        }
    }

    /**
     * 保存最新一条--供message-list使用,发送成功or失败暂时不管
     */
    fun saveLastMessage(msg: ChatMessageEntity?){
        if(msg == null) return
        if(mIsService) return

        val target = _targetUser.value
        viewModelScope.launch {
            // 先查数据库有没有此人-有则更新-无则新增
            if(mCurrentItemEntity == null){
                DatabaseFactory.getDatabase(CallmeApplication.context)
                    .getMessageListById(target?.userId?:""){ entity ->
                        if(entity == null){
                            // 数据库没有--新增
                            mCurrentItemEntity = MessageListEntity(
                                userId = target?.userId?:"",
                                currentUserId = UserInfoManager.myUserInfo?.userId?:"",
                                userName = target?.nickname?:"",
                                gender = target?.gender?:1,
                                unitPrice = target?.unitPrice?:0,
                                avatar = target?.avatar?:"",
                                avatarThumbUrl = target?.avatarThumbUrl?:"",
                                lastMessage = msg.content,
                                lastMessageType = msg.messageType,
                                timestamp = TimeUtils.formatTimestampForChatList(msg.timestamp),
                                timeInMillis = msg.timestamp,
                                unreadCount = 0, // 在这里都是已读
                                onlineStatus = _onlineStatus.value?:CallStatus.UNKNOWN,
                                isPinned = false
                            )
                            DatabaseFactory.getDatabase(CallmeApplication.context)
                                .insertMessageList(mCurrentItemEntity!!)

                            return@getMessageListById
                        }
                        // 数据库有--更新
                        mCurrentItemEntity = entity
                        mCurrentItemEntity?.apply {
                            lastMessage= if(msg.messageType == MessageType.GIFT) "" else msg.content
                            lastMessageType = msg.messageType
                            timestamp = TimeUtils.formatTimestampForChatList(msg.timestamp)
                            timeInMillis = msg.timestamp
                            unreadCount = 0 // 在这里都是已读
                            onlineStatus = _onlineStatus.value?:CallStatus.UNKNOWN
                        }
                        // 更新数据库
                        DatabaseFactory.getDatabase(CallmeApplication.context)
                            .updateCurrentUserMessageList(mCurrentItemEntity!!)
                    }
            }else {
                // update
                mCurrentItemEntity?.apply {
                    lastMessage = if(msg.messageType == MessageType.GIFT) "" else msg.content
                    lastMessageType = msg.messageType
                    timestamp = TimeUtils.formatTimestampForChatList(msg.timestamp)
                    timeInMillis = msg.timestamp
                    unreadCount = 0 // 在这里都是已读
                    onlineStatus = _onlineStatus.value?:CallStatus.UNKNOWN
                }
                DatabaseFactory.getDatabase(CallmeApplication.context)
                    .updateCurrentUserMessageList(mCurrentItemEntity!!)
            }
        }
    }
} 