package com.score.callmetest.ui.home

import android.annotation.SuppressLint
import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout
import com.score.callmetest.R
import com.score.callmetest.databinding.FragmentWallBinding
import com.score.callmetest.manager.FollowManager
import com.score.callmetest.network.BroadcasterModel
import com.score.callmetest.ui.base.BaseFragment
import com.score.callmetest.ui.home.adapter.BroadcasterAdapter
import com.score.callmetest.ui.widget.BlockEvent
import com.score.callmetest.util.EventBus
import timber.log.Timber

class WallFragment : BaseFragment<FragmentWallBinding, WallViewModel>() {
    private lateinit var recyclerView: RecyclerView
    private lateinit var swipeRefreshLayout: SwipeRefreshLayout
    private lateinit var broadcasterAdapter: BroadcasterAdapter
    private var isFragmentVisible = false
    private var tab1Name: String = ""
    private var tab2Name: String = ""
    private var emptyViewInflated: View? = null

    companion object {
        private const val ARG_TAB1_ID = "tab1_id"
        private const val ARG_TAB2_ID = "tab2_id"
        private const val ARG_REGION = "region"

        fun newInstance(tab1Name: String, tab2Name: String, region: String?): WallFragment {
            return WallFragment().apply {
                arguments = Bundle().apply {
                    putString(ARG_TAB1_ID, tab1Name)
                    putString(ARG_TAB2_ID, tab2Name)
                    putString(ARG_REGION, region)
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            tab1Name = it.getString(ARG_TAB1_ID, "") ?: ""
            tab2Name = it.getString(ARG_TAB2_ID, "") ?: ""
        }
    }

    override fun getViewBinding(inflater: LayoutInflater, container: ViewGroup?): FragmentWallBinding {
        return FragmentWallBinding.inflate(inflater, container, false)
    }

    override fun getViewModelClass() = WallViewModel::class.java

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): android.view.View? {
        binding = getViewBinding(inflater, container)
        // 通过自定义工厂传参创建 ViewModel
        viewModel = WallViewModel(tab1Name, tab2Name)
        // region参数在这里设置
        val region = arguments?.getString(ARG_REGION)
        viewModel.updateRegion(region)

        initView()
        initListener()
        initData()
        return binding.root
    }


    override fun initView() {
        // 初始化 RecyclerView
        recyclerView = binding.recyclerView
        recyclerView.setBackgroundColor(android.graphics.Color.WHITE)

        // 初始化 SwipeRefreshLayout
        swipeRefreshLayout = binding.swipeRefreshLayout.apply {
            setColorSchemeResources(R.color.refresh_loading)
        }

        // 初始化适配器和布局管理器
        broadcasterAdapter = BroadcasterAdapter()
        val layoutManager = GridLayoutManager(context, 2)
        recyclerView.layoutManager = layoutManager
        recyclerView.adapter = broadcasterAdapter

        // 注册数据和状态回调
        viewModel.onDataChanged = { list, _ ->
            broadcasterAdapter.submitList(list)
            swipeRefreshLayout.isRefreshing = false
            updateEmptyState(list.isEmpty())
        }
        viewModel.onStatusChanged = { list ->
            broadcasterAdapter.updateStatus(list)
        }
        viewModel.onError = {
            swipeRefreshLayout.isRefreshing = false
            // TODO: 显示错误提示
        }
    }

    override fun initListener() {
        super.initListener()

        // 监听滑动加载更多和滚动停止刷新
        recyclerView.addOnScrollListener(object : RecyclerView.OnScrollListener() {
            override fun onScrollStateChanged(recyclerView: RecyclerView, newState: Int) {
                super.onScrollStateChanged(recyclerView, newState)
                val isScrolling = newState != RecyclerView.SCROLL_STATE_IDLE
                viewModel.onScrollStateChanged(isScrolling) {
                    viewModel.startStatusUpdates {
                        if (isFragmentVisible) {
                            refreshVisibleBroadcasters()
                        }
                    }
                }
                EventBus.post(HomeViewModel.WallScrollEvent(newState))
            }
            override fun onScrolled(recyclerView: RecyclerView, dx: Int, dy: Int) {
                super.onScrolled(recyclerView, dx, dy)
                val lm = recyclerView.layoutManager as GridLayoutManager
                val visibleItemCount = lm.childCount
                val totalItemCount = lm.itemCount
                val firstVisibleItemPosition = lm.findFirstVisibleItemPosition()
                if (!viewModel.isLoading() && viewModel.hasMoreData()) {
                    if ((visibleItemCount + firstVisibleItemPosition) >= totalItemCount && firstVisibleItemPosition >= 0) {
                        viewModel.loadMore()
                    }
                }
            }
        })

        // 下拉刷新监听
        swipeRefreshLayout.setOnRefreshListener {
            viewModel.refresh()
        }

        // 监听关注和取消关注事件，当变化时重新加载列表
        EventBus.observe(this, FollowManager.FollowListRefreshEvent::class.java) { event ->
           //todo 由于ViewPager 或 TabLayout会有多个fragment页面导致多次刷新，但是这个不影响其他的监听
            if (tab1Name == "Followed") {
                // 当关注数量变化时，重新加载关注列表
                viewModel.refresh()
            }
        }
        EventBus.observe(this, BlockEvent::class.java) { event ->
            // 当有用户被拉黑时，从列表中移除被拉黑的用户
            viewModel.removeBlockedUser(event.userId)
        }

        // 监听国家筛选变化事件
        EventBus.observe(this, HomeViewModel.CountryFilterChangeEvent::class.java) { event ->
            // 只有Popular标签下的All子标签才响应国家筛选
            if (tab1Name == "Popular" && tab2Name == "All") {
                // 显示加载动画
                swipeRefreshLayout.isRefreshing = true
                viewModel.updateRegion(event.countryCode)
            }
        }
    }

    override fun initData() {
        super.initData()

        // 首次加载数据
        swipeRefreshLayout.isRefreshing = true
        viewModel.refresh()
    }

    private fun getVisibleBroadcasters(): List<BroadcasterModel> {
        val layoutManager = recyclerView.layoutManager as? GridLayoutManager ?: return emptyList()
        val firstVisible = layoutManager.findFirstVisibleItemPosition()
        val lastVisible = layoutManager.findLastVisibleItemPosition()
        if (firstVisible < 0 || lastVisible < 0) return emptyList()
        return (firstVisible..lastVisible)
            .mapNotNull { position ->
                if (position < broadcasterAdapter.currentList.size) {
                    broadcasterAdapter.currentList[position]
                } else null
            }
    }

    private fun refreshVisibleBroadcasters() {
        val visibleBroadcasters = getVisibleBroadcasters()
        viewModel.refreshVisibleBroadcasters(visibleBroadcasters)
    }

    /**
     * 更新空状态显示
     * @param isEmpty 数据是否为空
     */
    private fun updateEmptyState(isEmpty: Boolean) {
        if (isEmpty) {
            // 显示空状态，隐藏RecyclerView
            if (emptyViewInflated == null) {
                emptyViewInflated = binding.emptyView.inflate()
            }
            emptyViewInflated?.visibility = View.VISIBLE
            swipeRefreshLayout.visibility = View.GONE
        } else {
            // 隐藏空状态，显示RecyclerView
            emptyViewInflated?.visibility = View.GONE
            swipeRefreshLayout.visibility = View.VISIBLE
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onResume() {
        super.onResume()
        isFragmentVisible = true
        viewModel.startStatusUpdates {
            if (isFragmentVisible) {
                refreshVisibleBroadcasters()
            }
        }
        broadcasterAdapter?.notifyDataSetChanged()
    }

    override fun onPause() {
        super.onPause()
        isFragmentVisible = false
        viewModel.stopStatusUpdates()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        viewModel.clear()
    }
} 