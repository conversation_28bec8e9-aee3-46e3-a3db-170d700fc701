package com.score.callmetest.ui.home.adapter

import android.Manifest
import android.content.Intent
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.text.style.ImageSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.graphics.toColorInt
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import com.score.callmetest.CallStatus
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.manager.AppPermissionManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.SocketManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.network.BroadcasterModel
import com.score.callmetest.ui.broadcaster.BroadcasterDetailActivity
import com.score.callmetest.ui.videocall.VideoCallActivity
import com.score.callmetest.ui.widget.AlphaSVGAImageView
import com.score.callmetest.ui.widget.InsufficientBalanceDialog
import com.score.callmetest.util.CountryUtils
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.LoadingUtils
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.ToastUtils
import com.score.callmetest.util.click
import com.score.callmetest.util.logAsTag
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import timber.log.Timber

class BroadcasterAdapter :
    ListAdapter<BroadcasterModel, BroadcasterAdapter.ViewHolder>(DiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val view = LayoutInflater.from(parent.context)
            .inflate(R.layout.item_broadcaster, parent, false)
        return ViewHolder(view)
    }

    override fun onBindViewHolder(
        holder: ViewHolder,
        position: Int
    ) {
        holder.bind(this, getItem(position), position)
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int, payloads: MutableList<Any>) {
        if (payloads.isNotEmpty() && payloads[0] is String && payloads[0] == "status") {
            val broadcaster = getItem(position)
        } else {
            super.onBindViewHolder(holder, position, payloads)
        }
    }

    class ViewHolder(view: View) : RecyclerView.ViewHolder(view) {
        private val avatar: ImageView = view.findViewById(R.id.avatar)
        private val imageRegion: ImageView = view.findViewById(R.id.image_region)
        private val nickname: TextView = view.findViewById(R.id.nickname)
        private val tvPrice: TextView = view.findViewById(R.id.tv_price)
        private val statusLayout: ViewGroup = view.findViewById(R.id.status_layout)
        private val statusIndicator: View = view.findViewById(R.id.status_indicator)
        private val statusText: TextView = view.findViewById(R.id.status_text)
        private val btnVideoSvga: AlphaSVGAImageView = view.findViewById(R.id.btn_video_svga)
        private val btnVideoGray: ImageView = view.findViewById(R.id.btn_video_gray)
        private val bgMask: View = itemView.findViewById(R.id.bg_mask)

        // 用于管理动画循环的 Runnable
        private var animationRunnable: Runnable? = null




        fun bind(adapter: BroadcasterAdapter, broadcaster: BroadcasterModel, itemPosition: Int) {
            bgMask.background = DrawableUtils.createGradientDrawable(
                colors = intArrayOf(Color.TRANSPARENT, "#881B1C20".toColorInt()),
                orientation = GradientDrawable.Orientation.TOP_BOTTOM
            )
            // 加载头像 - 优先使用avatarThumbUrl，如果没有则使用avatar
            Glide.with(avatar)
                .load(broadcaster.avatar ?: broadcaster.avatarThumbUrl)
                .placeholder(R.drawable.placeholder)
                .error(R.drawable.placeholder)
                .into(avatar)

            // 设置文本信息
            nickname.text = broadcaster.nickname
            GlobalManager.setTextTypefaceSansSerif(nickname)

            // 设置国家图标 - 先将国家名称转换为ISO代码，再查找图标
            val countryName = broadcaster.country ?: "USA"
            val countryIconRes = if (countryName == "USA" || countryName == "United States") {
                CountryUtils.getIconByIsoFromLibrary("US")
            } else {
                CountryUtils.getIconByEnNameFromLibrary(countryName)
            }
            imageRegion.setImageResource(countryIconRes)

            // 设置价格文本 - 显示带金币图标的价格
            val price = broadcaster.callCoins ?: 0
            tvPrice.text = CustomUtils.createCoinSpannableText(
                context = itemView.context,
                text = "${price} icon/min",
                coinSizeDp = 9f,
                alignment = ImageSpan.ALIGN_BASELINE,
                spacesBefore = 0,
                spacesAfter = 1
            )

            // item点击事件
            itemView.click {
                val context = itemView.context
                val intent = Intent(context, BroadcasterDetailActivity::class.java)
                intent.putExtra("broadcaster_model", broadcaster)
                context.startActivity(intent)
            }

            GlobalManager.setViewRoundBackground(statusLayout, "#33000000".toColorInt())
            GlobalManager.setViewRoundBackground(
                statusIndicator, GlobalManager.getStatusColor(broadcaster.status)
            )
            statusText.text = broadcaster.status

            var status = UserInfoManager.getCachedStatus(broadcaster.userId) ?: broadcaster.status

            if (StrategyManager.isReviewPkg()) {
                if (broadcaster.isAnswer == true && !StrategyManager.reviewPkgUsers.contains(broadcaster.userId)) {
                    status = CallStatus.ONLINE
                } else {
                    status = GlobalManager.getReviewOtherStatus(broadcaster.userId)
                }
            }


            if (status == CallStatus.ONLINE ||
                status == CallStatus.AVAILABLE) {
                btnVideoSvga.visibility = View.VISIBLE
                btnVideoGray.visibility = View.GONE
                btnVideoSvga.isClickable = true

                // 清理之前的动画任务
                animationRunnable?.let { btnVideoSvga.removeCallbacks(it) }

                // 关键修复：只有当动画没有在播放时才重新播放
                if (!btnVideoSvga.isAnimating) {
                    startPeriodicAnimation()
                }
                btnVideoSvga.click {
                    val activity = itemView.context as? androidx.appcompat.app.AppCompatActivity
                    if (activity == null) {
//                        ToastUtils.showToast("页面异常，无法发起通话")
                        return@click
                    }
                    // 非审核模式下先判断金币是否足够
                    if (!StrategyManager.isReviewPkg()) {
                        val availableCoins = UserInfoManager.myUserInfo?.availableCoins ?: 0
                        val unitPrice = broadcaster.callCoins ?: 0
                        if (availableCoins < unitPrice) {

                            // 弹出金币充值弹窗（新版：自动拉取商品数据,显示主播每分钟收费）
                            val dialog = InsufficientBalanceDialog.newInstance(unitPrice)
                            dialog.show(activity.supportFragmentManager, "insufficient_balance")

                            return@click
                        }
                    }

                    // 检查网络连接
                    if (!SocketManager.instance.isConnected()) {
//                        ToastUtils.showToast("Socket service is offline")
                        Timber.d("Socket service is offline")
                        ToastUtils.showToast(CallmeApplication.context.getString(R.string.net_error_and_try_again))
                        return@click
                    }
                    // 3. 金币和网络检查通过后，再检查权限
                    AppPermissionManager.checkAndRequestCameraMicrophonePermission(
                        activity,
                        onGranted = {
                            // 权限授权成功，再次确认在线状态
                            LoadingUtils.showLoading(activity)
                            UserInfoManager.loadOnlineStatus(
                                scope = CoroutineScope(Dispatchers.IO),
                                userId = broadcaster.userId,
                                callback = { status, error ->
                                    ThreadUtils.runOnMain {
                                        LoadingUtils.dismissLoading()
                                        if (error == null && status != null) {
                                            status.toString().logAsTag(this.javaClass.name + "status: ")
                                            broadcaster.status = status
                                            adapter.notifyItemChanged(itemPosition)
                                            when(status) {
                                                CallStatus.ONLINE, CallStatus.AVAILABLE -> {
                                                    VideoCallActivity.startOutgoing(
                                                        context = activity,
                                                        userId = broadcaster.userId,
                                                        avatarUrl = broadcaster.avatarThumbUrl.toString(),
                                                        nickname = broadcaster.nickname.toString(),
                                                        age = broadcaster.age.toString(),
                                                        country = broadcaster.country.toString(),
                                                        unitPrice = broadcaster.unit.toString()
                                                    )
                                                }
                                                else -> {
                                                    ToastUtils.showToast(CallmeApplication.context.getString(R.string.user_not_available))
                                                }
                                            }
                                        }
                                    }
                                }
                            )
                        },
                        onDenied = {
                            ToastUtils.showToast(CallmeApplication.context.getString(R.string.camera_microphone_permission_required))
                            val s = AppPermissionManager.shouldShowRequestPermissionRationale(
                                activity,
                                Manifest.permission.RECORD_AUDIO
                            )
                            if(s) {
                                Timber.d("ask") // 可以再次询问权限
                            } else {
                                Timber.d("onDenied") // 权限被永久拒绝，跳转设置页面
                                ToastUtils.showToast(CallmeApplication.context.getString(R.string.camera_microphone_permission_check_hint))
                                AppPermissionManager.openAppSettings(activity)
                            }


                        }
                    )
                }


            } else {
                // 清理动画任务和停止 SVGA 动画
                animationRunnable?.let { btnVideoSvga.removeCallbacks(it) }
                btnVideoSvga.stopAnimation()
                btnVideoSvga.visibility = View.GONE
                btnVideoGray.visibility = View.VISIBLE
                btnVideoGray.isClickable = true
                btnVideoGray.click {
                    val activity = itemView.context as? androidx.appcompat.app.AppCompatActivity
                    if (activity == null) {
                        return@click
                    }

                    // 非审核模式下先判断金币是否足够
                    if (!StrategyManager.isReviewPkg()) {
                        val availableCoins = UserInfoManager.myUserInfo?.availableCoins ?: 0
                        val unitPrice = broadcaster.callCoins ?: 0
                        if (availableCoins < unitPrice) {
                            // 弹出金币充值弹窗（新版：自动拉取商品数据,显示主播每分钟收费）
                            val dialog = InsufficientBalanceDialog.newInstance(unitPrice)
                            dialog.show(activity.supportFragmentManager, "insufficient_balance")
                            return@click
                        }

                        // 金币充足但主播状态不可用时显示toast
                        val statusText = CallStatus.getDisplayText(broadcaster.status)
                        val message = activity.getString(R.string.user_status_not_available, statusText)
                        ToastUtils.showToast(message)
                    }
                }
            }
        }

        /**
         * 开始周期性动画播放
         * 性能优化：播放3次后暂停1秒，然后重复
         */
        private fun startPeriodicAnimation() {
            CustomUtils.playSvga(
                btnVideoSvga,
                "btn_broadcaster.svga",
                loops = 3, // 播放3次
                onFinished = {
                    // 动画完成后，间隔1秒再次播放
                    animationRunnable = Runnable {
                        if (btnVideoSvga.visibility == View.VISIBLE) {
                            startPeriodicAnimation()
                        }
                    }
                    btnVideoSvga.postDelayed(animationRunnable, 1000) // 1秒间隔
                }
            )
        }
    }

    private class DiffCallback : DiffUtil.ItemCallback<BroadcasterModel>() {
        override fun areItemsTheSame(
            oldItem: BroadcasterModel,
            newItem: BroadcasterModel
        ): Boolean {
            return oldItem.userId == newItem.userId
        }

        override fun areContentsTheSame(
            oldItem: BroadcasterModel,
            newItem: BroadcasterModel
        ): Boolean {
            return oldItem == newItem
        }
    }

    fun updateStatus(updatedList: List<BroadcasterModel>) {
        // 以当前列表为基础，生成新列表
        val newList = currentList.map { current ->
            val updated = updatedList.find { it.userId == current.userId }
            if (updated != null && current.status != updated.status) {
                current.copy(status = updated.status)
            } else {
                current
            }
        }
        submitList(newList)
    }
}