package com.score.callmetest.ui.message

import android.animation.ValueAnimator
import android.content.Context
import android.graphics.Color
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.animation.DecelerateInterpolator
import androidx.appcompat.widget.AppCompatImageView
import androidx.appcompat.widget.AppCompatTextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.score.callmetest.R
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.click
import timber.log.Timber
import kotlin.math.abs
import kotlin.math.max

/**
 * 消息弹窗视图
 * 用于显示收到新消息时的顶部弹窗
 * 
 * <AUTHOR>
 * @date 2025/07/30
 */
class MessageIncomingPopupView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr) {

    companion object {
        private const val TAG = "MsgIncomingPopupView"
    }

    // 回调函数
    private var onMessageClick: (() -> Unit)? = null
    private var onSwipeUp: (() -> Unit)? = null
    private var onDragStart: (() -> Unit)? = null
    private var onDragEnd: (() -> Unit)? = null

    // UI组件
    private val messageContainer: ConstraintLayout
    private val avatar: AppCompatImageView
    private val nickname: AppCompatTextView
    private val content: AppCompatTextView
    private val rightIcon: AppCompatImageView
    private val rootLayout: View


    // 拖拽相关
    private var isDragging = false
    private var initialY = 0f
    private var currentTranslationY = 0f
    private var dragAnimator: ValueAnimator? = null

    init {
        LayoutInflater.from(context).inflate(R.layout.layout_message_incoming_popup, this, true)

        // 初始化UI组件
        messageContainer = findViewById(R.id.messageContainer)
        avatar = findViewById(R.id.iv_avatar)
        nickname = findViewById(R.id.tv_nickname)
        content = findViewById(R.id.tv_content)
        rightIcon = findViewById(R.id.iv_right_icon)
        rootLayout = findViewById(R.id.root_layout)

        setBackgroundColor(Color.TRANSPARENT)

        setupClickListeners()
        setupGestureDetection()

        Timber.tag(TAG).d("MessageIncomingPopupView initialized")
    }

    /**
     * 设置点击监听器
     */
    private fun setupClickListeners() {
        // 消息容器点击事件
        messageContainer.click {
            Timber.tag(TAG).d("Message container clicked")
            onMessageClick?.invoke()
        }

        // 设置空的长按监听器，让系统能够正确区分click和longClick
        messageContainer.setOnLongClickListener {
            Timber.tag(TAG).d("Message container long clicked - ignored")
            true // 消费掉长按事件
        }

        // 禁用系统默认的点击反馈
        messageContainer.isHapticFeedbackEnabled = false

    }

    /**
     * 设置手势检测
     */
    private fun setupGestureDetection() {
        messageContainer.setOnTouchListener { view, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    initialY = event.rawY
                    isDragging = false
                    dragAnimator?.cancel()
                    Timber.tag(TAG).d("Touch down")
                }
                MotionEvent.ACTION_MOVE -> {
                    val deltaY = event.rawY - initialY

                    // 检测是否开始拖拽（垂直方向移动超过20px）
                    if (!isDragging && abs(deltaY) > 20) {
                        isDragging = true
                        onDragStart?.invoke()
                        Timber.tag(TAG).d("Drag started")
                    }

                    if (isDragging && deltaY < 0) { // 只允许向上拖拽
                        currentTranslationY = deltaY
                        messageContainer.translationY = currentTranslationY

                        // 根据拖拽距离调整透明度
                        val alpha = max(0.3f, 1f - abs(deltaY) / 300f)
                        messageContainer.alpha = alpha
                    }
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    if (isDragging) {
                        val deltaY = event.rawY - initialY
                        if (deltaY < -100) { // 向上拖拽超过100px则收起
                            Timber.tag(TAG).d("Swipe up threshold reached, dismissing")
                            onSwipeUp?.invoke()
                        } else {
                            // 回弹动画
                            animateToOriginalPosition()
                        }
                        isDragging = false
                        onDragEnd?.invoke()
                    }
                }
            }

            // 如果正在拖拽，消费事件；否则不消费，让click/longClick正常工作
            isDragging
        }
    }

    /**
     * 回弹到原始位置的动画
     */
    private fun animateToOriginalPosition() {
        dragAnimator?.cancel()
        dragAnimator = ValueAnimator.ofFloat(currentTranslationY, 0f).apply {
            duration = 200
            interpolator = DecelerateInterpolator()
            addUpdateListener { animator ->
                val value = animator.animatedValue as Float
                messageContainer.translationY = value
                messageContainer.alpha = 1f - abs(value) / 300f * 0.7f + 0.3f
            }
            start()
        }
        currentTranslationY = 0f
    }

    /**
     * 绑定消息数据
     *
     * @param avatarUrl 头像URL
     * @param nicknameStr 昵称
     * @param contentStr 消息内容
     * @param onMessageClick 消息点击回调
     * @param onSwipeUp 上滑回调
     * @param onDragStart 拖拽开始回调
     * @param onDragEnd 拖拽结束回调
     */
    fun bind(
        avatarUrl: String?,
        nicknameStr: String?,
        contentStr: String?,
        onMessageClick: (() -> Unit)? = null,
        onSwipeUp: (() -> Unit)? = null,
        onDragStart: (() -> Unit)? = null,
        onDragEnd: (() -> Unit)? = null
    ) {
        Timber.tag(TAG).d("Binding message data: nickname=$nicknameStr, content=$contentStr")

        // 设置回调
        this.onMessageClick = onMessageClick
        this.onSwipeUp = onSwipeUp
        this.onDragStart = onDragStart
        this.onDragEnd = onDragEnd

        updateContent(avatarUrl, nicknameStr, contentStr)
    }

    /**
     * 更新消息内容
     *
     * @param avatarUrl 头像URL
     * @param nicknameStr 昵称
     * @param contentStr 消息内容
     */
    fun updateContent(
        avatarUrl: String?,
        nicknameStr: String?,
        contentStr: String?
    ) {
        Timber.tag(TAG).d("Updating content: nickname=$nicknameStr, content=$contentStr")

        // 加载头像
        GlideUtils.load(
            view = avatar,
            url = avatarUrl,
            placeholder = R.drawable.placeholder,
            isCircle = true,
        )

        // 设置昵称
        nickname.text = nicknameStr ?: ""

        // 设置消息内容
        content.text = contentStr ?: ""

        // 设置右侧箭头图标
        rightIcon.setImageResource(R.drawable.ic_chevron_right)
    }

    /**
     * 设置消息点击监听器
     */
    fun setOnMessageClickListener(listener: () -> Unit) {
        this.onMessageClick = listener
    }


    /**
     * 设置上滑手势监听器
     */
    fun setOnSwipeUpListener(listener: () -> Unit) {
        this.onSwipeUp = listener
    }

}
