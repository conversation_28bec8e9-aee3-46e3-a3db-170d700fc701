package com.score.callmetest.ui.splash

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import android.util.Log
import android.view.View
import android.widget.Toast
import androidx.lifecycle.lifecycleScope
import com.score.callmetest.databinding.ActivitySplashBinding
import com.score.callmetest.ui.base.BaseActivity
import com.score.callmetest.ui.login.LoginActivity
import com.score.callmetest.manager.AppConfigManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.ui.main.MainActivity
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.ThreadUtils
import com.score.callmetest.util.ToastUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber

@SuppressLint("CustomSplashScreen")
class SplashActivity : BaseActivity<ActivitySplashBinding, SplashViewModel>() {
    
    override fun getViewBinding(): ActivitySplashBinding {
        return ActivitySplashBinding.inflate(layoutInflater)
    }

    override fun getViewModelClass() = SplashViewModel::class.java

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // 请求应用配置
        requestAppConfig()
    }

    override fun initView() {
        CustomUtils.playSvgaOnce(binding.splashLogo, "splash_logo.svga")
    }

    override fun initListener() {
        // Splash界面不需要监听器
    }

    /**
     * 请求应用配置
     */
    private fun requestAppConfig() {
        requestNewAppConfig()
    }

    /**
     * 请求新的应用配置
     */
    private fun requestNewAppConfig() {
        AppConfigManager.getAppConfig(
            onSuccess = { appConfigData ->
                // 保存配置
                AppConfigManager.saveAppConfig(appConfigData)
                onGetConfigSuccess()
            },
            onError = { errorMsg ->
                navigateToLogin()
            }
        )
    }

    private fun onGetConfigSuccess() {
        try {
            lifecycleScope.launch {
                val userinfo = UserInfoManager.fetchUserInfo("")
                if (userinfo == null) {
                    navigateToLogin()
                } else {
                    Timber.tag("SplashActivity").d(userinfo.toString())
                    UserInfoManager.updateMyUserInfo(userinfo)

                    viewModel.getStrategy(
                        onSuccess = {
                            navigateToMain()
                        },
                        onError = {
                            navigateToLogin()
                        }
                    )
                }
            }
        } catch (e: Throwable) {
            e.printStackTrace()
        }
    }

    /**
     * 导航到下一个界面
     */
    private fun navigateToLogin() {
        val intent = Intent(this, LoginActivity::class.java)
        startActivity(intent)
        finish()
    }

    /**
     * 导航到下一个界面
     */
    private fun navigateToMain() {
        val intent = Intent(this, MainActivity::class.java)
        startActivity(intent)
        finish()
    }
} 