package com.score.callmetest.ui.widget

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.graphics.drawable.Drawable
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.text.style.ImageSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.annotation.DrawableRes
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import androidx.core.graphics.toColorInt
import androidx.core.view.doOnPreDraw
import com.google.common.collect.BiMap
import com.score.callmetest.R
import com.score.callmetest.databinding.DialogCustomBinding
import com.score.callmetest.databinding.DialogFlashChatBinding
import com.score.callmetest.databinding.DialogFlashChatWelcomeBinding
import com.score.callmetest.manager.FlashChatManager
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.ui.match.MatchActivity
import com.score.callmetest.util.ActivityUtils
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.click

class FlashChatWelcomeDialog(
    val activity: AppCompatActivity,
) : Dialog(activity, androidx.appcompat.R.style.Theme_AppCompat_Dialog) {
    private var binding: DialogFlashChatWelcomeBinding = DialogFlashChatWelcomeBinding.inflate(LayoutInflater.from(context))

    @SuppressLint("SetTextI18n")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        // 设置全屏/居中
        window?.setLayout(
            ViewGroup.LayoutParams.MATCH_PARENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        // 隐藏状态栏
        window?.decorView?.systemUiVisibility = (
                View.SYSTEM_UI_FLAG_FULLSCREEN or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                )
        window?.setBackgroundDrawableResource(R.color.transparent)
        // 点击空白区域可取消
//        setCanceledOnTouchOutside(false)

        // 设置内容左右37dp边距
//        val paddingPx = (37 * context.resources.displayMetrics.density).toInt()
//        binding.root.setPadding(
//            paddingPx,
//            binding.root.paddingTop,
//            paddingPx,
//            binding.root.paddingBottom
//        )


        binding.welcome.setTypeface(Typeface.create("sans-serif", Typeface.BOLD));
        binding.btnMatch.setTypeface(Typeface.create("sans-serif", Typeface.BOLD));

        val time = "${FlashChatManager.matchFreeTimes}"
        val content =
            "Congratulations! $time Match Cards for you!! "
        val spannable = SpannableString(content)
        val startIndex = content.indexOf(time.toString())
        val endIndex = startIndex + time.toString().length
        spannable.setSpan(
            ForegroundColorSpan("#FF7500".toColorInt()),
            startIndex,
            endIndex,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        binding.title.text = spannable

        binding.btnMatch.doOnPreDraw {
            binding.btnMatch.background = DrawableUtils.createGradientDrawable(
                colors = intArrayOf("#FFDC20".toColorInt(), "#FF462F".toColorInt()),
                orientation = GradientDrawable.Orientation.TOP_BOTTOM,
                radius = binding.btnMatch.height / 2f
            )
        }

        binding.btnMatch.click {
            ActivityUtils.startActivity(context, MatchActivity::class.java)
            dismiss()
        }
    }
}