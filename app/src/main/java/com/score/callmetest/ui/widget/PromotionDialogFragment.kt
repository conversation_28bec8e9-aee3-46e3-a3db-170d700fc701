package com.score.callmetest.ui.widget

import android.annotation.SuppressLint
import android.content.DialogInterface
import android.graphics.Color
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import android.os.CountDownTimer
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.core.graphics.drawable.toDrawable
import androidx.core.graphics.toColorInt
import androidx.fragment.app.DialogFragment
import com.opensource.svgaplayer.SVGADrawable
import com.opensource.svgaplayer.SVGAParser
import com.opensource.svgaplayer.SVGAVideoEntity
import com.score.callmetest.R
import com.score.callmetest.util.CustomUtils
import com.score.callmetest.util.DrawableUtils
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.click
import java.util.concurrent.TimeUnit

class PromotionDialogFragment : DialogFragment() {

    private var amount: String = ""
    private var addAmount: String = ""
    private var description: String = ""
    private var price: Double = 0.0
    private var oldPrice: Double = 0.0
    private var buttonSvgaName: String = "sweep_button.svga"
    private var buttonEffectSvgaName: String = "tags_lucky.svga"
    private var countdownSeconds: Long = 0
    private var remainingCount: Int = 0
    private var onButtonClickListener: (() -> Unit)? = null
    private var layoutResId: Int = R.layout.dialog_promotion
    private var treasureBoxImageUrl: String? = null
    private var promotionStartTimeMillis: Long? = null

    private var countDownTimer: CountDownTimer? = null

    companion object {
        fun newInstance(
            layoutResId: Int = R.layout.dialog_promotion,
            amount: String,
            description: String,
            price: Double = 0.0,
            originPrice:Double = 0.0,
            countdownSeconds: Long = 0,
            buttonSvgaName: String = "sweep_button.svga",
            buttonEffectSvgaName: String = "tags_lucky.svga",
            onButtonClickListener: (() -> Unit)? = null,
            addAmount: String = "",
            remainingCount: Int = 0,
            treasureBoxImageUrl: String? = null,
            promotionStartTimeMillis: Long? = null
        ): PromotionDialogFragment {
            return PromotionDialogFragment().apply {
                this.layoutResId = layoutResId
                this.amount = amount
                this.addAmount = addAmount
                this.description = description
                this.price = price
                this.oldPrice = originPrice
                this.countdownSeconds = countdownSeconds
                this.buttonSvgaName = buttonSvgaName
                this.buttonEffectSvgaName = buttonEffectSvgaName
                this.onButtonClickListener = onButtonClickListener
                this.remainingCount = remainingCount
                this.treasureBoxImageUrl = treasureBoxImageUrl
                this.promotionStartTimeMillis = promotionStartTimeMillis // 赋值
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        // 禁止点击外部和返回键关闭
        isCancelable = false
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        dialog?.requestWindowFeature(Window.FEATURE_NO_TITLE)
        dialog?.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        return inflater.inflate(layoutResId, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 设置弹窗大小
        dialog?.window?.setLayout(
            ViewGroup.LayoutParams.WRAP_CONTENT,
            ViewGroup.LayoutParams.WRAP_CONTENT
        )

        // 设置蒙层透明度
        dialog?.window?.setDimAmount(0.6f)

        // 初始化视图
        setupViews(view)

        // 加载SVGA动画
        loadSvgaAnimations(view)

        // 开始倒计时
        startCountdown(view)
    }

    private fun setupViews(view: View) {
        // 设置宝箱图片 - 支持网络图片加载
        val ivTreasureBox = view.findViewById<ImageView>(R.id.iv_treasure_box)
        ivTreasureBox?.let {
            //todo。strategyConfig?.activityCategoryImageUrl 为null
            if (!treasureBoxImageUrl.isNullOrEmpty()) {
                // 使用网络图片
                GlideUtils.load(
                    context = requireContext(),
                    url = treasureBoxImageUrl,
                    imageView = it,
                    placeholder = R.drawable.promotion_action_src,
                    error = R.drawable.promotion_action_src
                )
            } else {
                // 使用默认图片
                GlideUtils.load(
                    context = requireContext(),
                    url = R.drawable.promotion_box,
                    imageView = it
                )
            }
        }

        // 设置金额文本
        val tvAmount = view.findViewById<TextView>(R.id.tv_amount)
        tvAmount?.text = amount

        // 设置额外金额文本
        val tvAddAmount = view.findViewById<TextView>(R.id.tv_add_amount)
        tvAddAmount?.text = addAmount

        // 设置描述文本
        val tvDescription = view.findViewById<TextView>(R.id.tv_description)
        if (description.isEmpty()) {
            tvDescription.visibility = View.GONE
        } else {
            tvDescription.visibility = View.VISIBLE
            tvDescription?.text = description
        }


        // 设置价格文本
        val tvPrice = view.findViewById<TextView>(R.id.tv_price)
        tvPrice?.text = price.toString()

        val price_layout = view.findViewById<LinearLayout>(R.id.price_layout)
        price_layout.click {
            onButtonClickListener?.invoke()
            dismiss()
        }

        // 设置原价文本
        val tvOldPrice = view.findViewById<TextView>(R.id.tv_old_price)
        if (tvOldPrice != null) {
            if (oldPrice > 0 && oldPrice > price) {
                tvOldPrice.text = oldPrice.toString()
                tvOldPrice.paint.isStrikeThruText = true  // 添加划线效果
                tvOldPrice.visibility = View.VISIBLE
            } else {
                tvOldPrice.visibility = View.GONE
            }
        }

        // 设置按钮文字 - 适用于新用户促销弹窗布局
        val tvButtonText = view.findViewById<TextView>(R.id.tv_button_text)
        tvButtonText?.text = "Claim"

        // 设置剩余数量文本
        val tvAccountDown = view.findViewById<TextView>(R.id.tv_account_down)
        if (tvAccountDown != null) {
            val text = "Only $remainingCount left"
            val spannable = android.text.SpannableString(text)
            val startIndex = text.indexOf(remainingCount.toString())
            val endIndex = startIndex + remainingCount.toString().length
            spannable.setSpan(
                android.text.style.ForegroundColorSpan("#FFFEFF00".toColorInt()),
                startIndex,
                endIndex,
                android.text.Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            tvAccountDown.text = spannable
        }

        // 设置关闭按钮点击事件
        val ivClose = view.findViewById<ImageView>(R.id.iv_close)
        ivClose?.click {
            dismiss()
        }
    }

    private fun loadSvgaAnimations(view: View) {
        val parser = SVGAParser(requireContext())

        // 加载按钮动画
        val svgaButton = view.findViewById<AlphaSVGAImageView>(R.id.svga_button)
        svgaButton.click {
            onButtonClickListener?.invoke()
            dismiss()
        }
        CustomUtils.playSvga(svgaButton, buttonSvgaName)

        // 加载按钮特效动画
        val svgaButtonEffect = view.findViewById<AlphaSVGAImageView>(R.id.svga_button_effect)
        CustomUtils.playSvga(svgaButtonEffect, buttonEffectSvgaName)
    }

    private fun startCountdown(view: View) {
        val tvCountdown = view.findViewById<TextView>(R.id.tv_countdown)
        // 如果倒计时文本不存在，直接返回，这适用于新用户促销弹窗布局
        if (tvCountdown == null) {
            return
        }

        // 设置倒计时背景
        val cornerRadiiPromotion = floatArrayOf(
            100f, 100f,     // 左上角 x,y 半径
            100f, 100f,   // 右上角 x,y 半径
            100f, 100f,   // 右下角 x,y 半径
            13f, 13f        // 左下角 x,y 半径
        )
        // 设置倒计时背景
        val cornerRadiiPromotion1 = floatArrayOf(
            100f, 100f,     // 左上角 x,y 半径
            100f, 100f,   // 右上角 x,y 半径
            13f, 13f,   // 右下角 x,y 半径
            100f, 100f        // 左下角 x,y 半径
        )

        val backgroundColor = "#ff391805".toColorInt()
        val countdownBackground: GradientDrawable;
        if (layoutResId == R.layout.dialog_promotion) {
            countdownBackground =
                DrawableUtils.createRoundRectDrawable(backgroundColor, cornerRadiiPromotion)
        } else if (layoutResId == R.layout.dialog_promotion1) {
            countdownBackground =
                DrawableUtils.createRoundRectDrawable(backgroundColor, cornerRadiiPromotion1)
        } else {
            return
        }

        tvCountdown.background = countdownBackground

        // 计算剩余时间
        val remainSeconds = if (promotionStartTimeMillis != null) {
            val now = System.currentTimeMillis()
            val elapsed = (now - promotionStartTimeMillis!!) / 1000
            (countdownSeconds - elapsed).coerceAtLeast(0)
        } else {
            countdownSeconds
        }
        countDownTimer?.cancel()
        countDownTimer = object : CountDownTimer(remainSeconds * 1000, 1000) {
            @SuppressLint("DefaultLocale")
            override fun onTick(millisUntilFinished: Long) {
                try {
                    // 检查Fragment和View是否还存在
                    if (isAdded && !isDetached && view != null) {
                        val hours = TimeUnit.MILLISECONDS.toHours(millisUntilFinished)
                        val minutes = TimeUnit.MILLISECONDS.toMinutes(millisUntilFinished) -
                                TimeUnit.HOURS.toMinutes(hours)
                        val seconds = TimeUnit.MILLISECONDS.toSeconds(millisUntilFinished) -
                                TimeUnit.MINUTES.toSeconds(
                                    TimeUnit.MILLISECONDS.toMinutes(
                                        millisUntilFinished
                                    )
                                )
                        tvCountdown.text = String.format("%02d:%02d:%02d", hours, minutes, seconds)
                    }
                } catch (e: Exception) {
                    // 如果更新UI失败，取消倒计时避免继续出错
                    countDownTimer?.cancel()
                }
            }

            override fun onFinish() {
                try {
                    // 检查Fragment和View是否还存在
                    if (isAdded && !isDetached && view != null) {
                        tvCountdown.text = "00:00:00"
                    }
                } catch (e: Exception) {
                    // 忽略异常，因为倒计时已经结束
                }
            }
        }.start()
    }

    override fun onDestroyView() {
        countDownTimer?.cancel()
        countDownTimer = null
        super.onDestroyView()
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        // 通知父Fragment弹窗已关闭
        parentFragmentManager.setFragmentResult("PromotionDialog_dismissed", Bundle())
    }
}