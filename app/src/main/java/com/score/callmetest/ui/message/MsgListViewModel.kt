package com.score.callmetest.ui.message

import android.os.Looper
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope

import com.score.callmetest.CallStatus
import com.score.callmetest.CallmeApplication
import com.score.callmetest.db.DatabaseFactory
import com.score.callmetest.entity.MessageListEntity
import com.score.callmetest.entity.isSysService
import com.score.callmetest.im.RongCloudManager
import com.score.callmetest.im.callback.ImOperationCallback
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.manager.UserInfoManager.getUserInfo
import com.score.callmetest.network.UserInfo
import com.score.callmetest.ui.base.BaseViewModel
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.launch
import timber.log.Timber
import kotlin.String
import kotlin.collections.contains

/**
 * 消息列表ViewModel
 * 处理消息列表的数据逻辑
 */
class MsgListViewModel: BaseViewModel() {

    companion object {
        private const val TAG = "MsgListViewModel"
    }

    // 消息列表数据
    private val _goChatUser = MutableLiveData<UserInfo>()
    val mGoChatUser: LiveData<UserInfo> = _goChatUser

    // 消息列表数据
    private val _messageList = MutableLiveData<List<MessageListEntity>>()
    val mMessageList: LiveData<List<MessageListEntity>> = _messageList
    
    // 加载状态
    private val _isLoading = MutableLiveData<Boolean>()
    val mIsLoading: LiveData<Boolean> = _isLoading
    
    // 错误信息
    private val _errorMessage = MutableLiveData<String>()
    val mErrorMessage: LiveData<String> = _errorMessage
    

    
    init {
        // 初始化时加载消息列表
        loadMessageList()

        // 设置MessageIncomingManager的回调
        MessageIncomingManager.setMessageListCallback { messageListEntity ->
            onNewMessageReceived(messageListEntity)
        }

        Timber.tag(MsgListViewModel::class.java.simpleName)
    }

    override fun onCleared() {
        super.onCleared()
        // 清理MessageIncomingManager的回调
        MessageIncomingManager.setMessageListCallback(null)
    }

    /**
     * 加载消息列表
     */
    fun loadMessageList() {
        _isLoading.value = true

        viewModelScope.launch {
            try {
                // 数据库读取
                DatabaseFactory.getDatabase(CallmeApplication.context)
                    .getCurrentUserAllMessageLists()
                   .catch { e ->
                       Timber.e("getCurrentUserAllMessageLists---$e")
                       _errorMessage.value = "加载消息列表失败: ${e.message}"
                       _isLoading.value = false
                   }.collect { list ->
                        Timber.d("collect....${Thread.currentThread()}")
                        updateMessageListPre { currentList ->
                            // 重新刷新--忽略当前list
                            // 检查list中有没有客服item，有则删除
                            val index = list.indexOfFirst { it.isSysService() }
                            if(index != -1 && StrategyManager.isReviewPkg()){
                                // 审核模式 && 存在--->删除，不显示客服
                                (list as MutableList).removeAt(index)
                            }else if(index == -1 && !StrategyManager.isReviewPkg()) {
                                // 不存在 && 非审核模式  -- > 添加客服item
                                (list as MutableList).add(MessageListEntity.provideCustomService())
                            }
                            list
                        }

                        _isLoading.value = false
                    }
            } catch (e: Exception) {
                _errorMessage.value = "加载消息列表失败: ${e.message}"
                _isLoading.value = false
            }
        }
    }

    /**
     *
     * 点击item查询userinfo后跳转
     */
    fun gotoChat(userId: String){
        // 先查缓存，再网络查询
        UserInfoManager.getUserInfo(userId){ getUserInfo ->
            getUserInfo?.let { nonNullUser ->
                _goChatUser.value = nonNullUser
            }
        }
    }

    /**
     * 更新状态
     * @param [statusMap] 状态map
     */
    fun updateStatus(statusMap: Map<String, String>) {

        updateMessageListPre { currentList ->
            currentList.map { map ->
                if(statusMap.containsKey(map.userId)){
                    // 存在--以map的状态为主
                    val newMap = map.copy(
                        onlineStatus = statusMap[map.userId] ?: CallStatus.UNKNOWN
                    )
                    // 更新数据库
                    DatabaseFactory.getDatabase(CallmeApplication.context)
                        .updateStatus(newMap.userId, newMap.onlineStatus)

                    return@map newMap
                }else  return@map map
            }
        }
    }

    /**
     * 更新列表数据（简化版本，不再管理底部项）
     *
     * @param messageList
     * @param isSetValue 是否使用LiveData.postValue()执行--不在主线程只能postValue
     *
     */
    private fun updateList(messageList: List<MessageListEntity>, isSetValue: Boolean = true) {
        // 简化逻辑：只保留实际的消息数据，底部项由 Fragment 控制
        val list = messageList.filter { !it.isBottomView }

        if(isSetValue && (Looper.getMainLooper() === Looper.myLooper())) {
            _messageList.value = list
        }else {
            _messageList.postValue(list)
        }
    }



    // <editor-fold desc="im相关操作">

    /**
     * 处理从MessageIncomingManager接收到的新消息
     * 替代原来的融云消息监听
     */
    private fun onNewMessageReceived(messageListEntity: MessageListEntity) {
        Timber.tag(TAG).d("onNewMessageReceived...${Thread.currentThread()}")
        // 子线程
        Timber.tag(TAG).d("Received new message entity for user: ${messageListEntity.userId}")

        updateMessageListPre { currentList ->
            // 寻找当前用户是否已存在于列表中
            val existingIndex = currentList.indexOfFirst { item ->
                item.userId == messageListEntity.userId
            }

            if (existingIndex >= 0) {
                // 存在 - 更新现有项
                currentList[existingIndex] = messageListEntity
                Timber.tag(TAG).d("Updated existing message list item for user: ${messageListEntity.userId}")
                currentList
            } else {
                // 不存在 - 添加新项
                Timber.tag(TAG).d("Added new message list item for user: ${messageListEntity.userId}")
                currentList.apply {
                    add(messageListEntity)
                }
            }
        }
    }


// </editor-fold>

    
    /**
     * 对消息列表进行排序
     * 置顶的消息排在前面，然后按时间戳排序
     * @param list 原始消息列表
     * @return 排序后的消息列表
     */
    /**
     * 对消息列表进行排序
     * 规则：
     * 1. 置顶消息在前面
     * 2. 同类型消息按时间倒序排列（最新的在前面）
     */
    private fun sortMessageList(list: List<MessageListEntity>): List<MessageListEntity> {
        return list.sortedWith(
            compareByDescending<MessageListEntity> {
                // 1. 系统优先
                it.isSysService()
            }
                .thenByDescending { it.isPinned }                       // 2. 置顶优先
                .thenByDescending { it.timeInMillis }                   // 3. 时间倒序
//                    .thenBy { it.senderName }                               // 4. 发送者名称正序
        ).map { entity ->
            // 适配审核模式
            if (StrategyManager.isReviewPkg()) {
                getUserInfo(entity.userId) { userInfo ->
                    if (userInfo?.isAnswer == true && !StrategyManager.reviewPkgUsers.contains(userInfo.userId)) {
                        entity.onlineStatus = CallStatus.ONLINE
                    }else {
                        entity.onlineStatus = GlobalManager.getReviewOtherStatus(userInfo?.userId)
                    }
                }
            }
            entity
        }
    }




    // <editor-fold desc="popupView相关操作">
    /**
     * 一键已读
     */
    fun clearUnreadMessages(){
        updateMessageListPre { currentList ->
            currentList.map {
                if(it.unreadCount > 0) {
                    // 数据库也同步更新
                    DatabaseFactory.getDatabase(CallmeApplication.context)
                        .updateUnreadCount(it.userId, 0)

                    it.copy(unreadCount = 0)
                }
                else it
            }
        }
    }
    
    /**
     * 隐藏消息
     * @param messageObj 要隐藏的消息
     */
    fun hideMessage(messageObj: MessageListEntity) {
        updateMessageListPre { currentList ->
            // todo dsc-- 隐藏和删除有什么区别？。。
            currentList.filter { it.userId != messageObj.userId }
        }
    }
    
    /**
     * 删除消息
     * @param messageObj 要删除的消息
     */
    fun deleteMessage(messageObj: MessageListEntity) {
        updateMessageListPre { currentList ->
            // 数据库删除
            DatabaseFactory.getDatabase(CallmeApplication.context)
                .deleteMessageList(messageObj)
            // 融云删除
            if(messageObj.userId.isNotEmpty()) {
                val now = System.currentTimeMillis()
                // 融云的本地数据库数据也删
                RongCloudManager.cleanHistoryMessages(
                    messageObj.userId,
                    now,
                    true,
                    object : ImOperationCallback() {
                        override fun success() {
                        }

                        override fun error(code: Int?, errorMsg: String?) {
                            Timber.e("deleteMessage error: $code, $errorMsg")
                        }
                    })
            }
            // 本地删除
            currentList.filter { it.userId != messageObj.userId }

        }
    }
    
    /**
     * 置顶/取消置顶消息
     * @param messageObj 消息对象
     * @param isPinned 是否置顶
     */
    fun pinMessage(messageObj: MessageListEntity, isPinned: Boolean) {
        updateMessageListPre { currentList ->
            currentList.map {
                if (it.userId == messageObj.userId) {
                    // 修改数据库
                    DatabaseFactory.getDatabase(CallmeApplication.context)
                        .updatePinStatus(messageObj.userId, isPinned)
                    // 修改本地
                    it.copy(isPinned = isPinned)
                } else {
                    it
                }
            }
        }
    }

    // </editor-fold>
    
    /**
     * 更新当前消息列表的通用方法
     *
     * @param isSetValue 是否使用 setValue
     * @param update 更新操作
     */
    private fun updateMessageListPre(isSetValue: Boolean = true,update: (MutableList<MessageListEntity>) -> List<MessageListEntity>) {
        viewModelScope.launch {
            val currentList = _messageList.value?.toMutableList() ?: mutableListOf()
            val updatedList = update(currentList)

            // 排序消息列表并重新添加底部项（如果需要）
            updateList(sortMessageList(updatedList),isSetValue)
        }
    }
}