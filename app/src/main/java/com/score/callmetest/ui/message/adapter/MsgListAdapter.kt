package com.score.callmetest.ui.message.adapter

import android.content.Context
import android.graphics.Point
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.PopupWindow
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.score.callmetest.CallStatus
import com.score.callmetest.CallmeApplication
import com.score.callmetest.R
import com.score.callmetest.databinding.ItemListBottomBinding
import com.score.callmetest.databinding.ItemMsgVpBinding
import com.score.callmetest.entity.MessageListEntity
import com.score.callmetest.entity.MessageType
import com.score.callmetest.entity.isSysService
import com.score.callmetest.manager.GlobalManager
import com.score.callmetest.manager.StrategyManager
import com.score.callmetest.manager.UserInfoManager
import com.score.callmetest.manager.UserInfoManager.getUserInfo
import com.score.callmetest.util.ClickUtils
import com.score.callmetest.util.DeviceUtils
import com.score.callmetest.util.DisplayUtils
import com.score.callmetest.util.GlideUtils
import com.score.callmetest.util.click
import timber.log.Timber
import kotlin.collections.contains
import kotlin.collections.set

/**
 * 消息列表适配器
 * 实现消息列表的展示和长按菜单功能
 */
class MsgListAdapter : ListAdapter<MessageListEntity, RecyclerView.ViewHolder>(DiffCallback()) {

    companion object {
        private const val VIEW_TYPE_MESSAGE = 0
        private const val VIEW_TYPE_BOTTOM = 1
    }

    // 记录长按的位置
    private val mTouchPoint: Point = Point()

    // 当前显示的菜单
    private var mPopupWindow: PopupWindow? = null

    // popupview相关
    private val mInflater =
        CallmeApplication.context.getSystemService(Context.LAYOUT_INFLATER_SERVICE) as LayoutInflater
    private var mPopupView: View? = null

    // 用于计算popupView显示位置
    private var mMenuHight: Int = 0
    private var mMenuWidth: Int = 0
    private val _screenHight: Int = DeviceUtils.getScreenHeight(CallmeApplication.context)
    private val _screenWidth: Int = DeviceUtils.getScreenWidth(CallmeApplication.context)
    private val ANCHORED_GRAVITY = Gravity.TOP or Gravity.START
    private val VERTICAL_OFFSET: Int = DisplayUtils.dp2px(10f)

    // popupView text
    private val _strHiden: String by lazy { CallmeApplication.context.getString(R.string.msg_item_menu_hide) }
    private val _strDelete: String by lazy { CallmeApplication.context.getString(R.string.msg_item_menu_delete) }
    private val _strPin: String by lazy { CallmeApplication.context.getString(R.string.msg_item_menu_pin) }
    private val _strUnpin: String by lazy { CallmeApplication.context.getString(R.string.msg_item_menu_unpin) }

    // 消息类型
    private val _strImage: String by lazy { CallmeApplication.context.getString(R.string.msg_list_type_img) }
    private val _strLink: String by lazy { CallmeApplication.context.getString(R.string.msg_list_type_link) }
    private val _strVoice: String by lazy { CallmeApplication.context.getString(R.string.msg_list_type_voice) }
    private val _strGift: String by lazy { CallmeApplication.context.getString(R.string.msg_list_type_gift) }
    private val _strFile: String by lazy { CallmeApplication.context.getString(R.string.msg_list_type_file) }
    private val _strVideo: String by lazy { CallmeApplication.context.getString(R.string.msg_list_type_video) }
    private val _strRechargeCard: String by lazy { CallmeApplication.context.getString(R.string.msg_list_type_recharge_card) }
    private val _strSysService: String by lazy { CallmeApplication.context.getString(R.string.msg_service_welcome) }

    /**
     * 消息项点击监听器
     */
    interface OnMessageItemClickListener {

        /**
         * 当消息项的头像被点击时回调
         * @param position 点击的位置
         * @param messageObj 点击的消息对象
         */
        fun onItemAvatarClick(position: Int, messageObj: MessageListEntity)


        /**
         * 当消息项被点击时回调
         * @param position 点击的位置
         * @param messageObj 点击的消息对象
         */
        fun onItemClick(position: Int, messageObj: MessageListEntity)

        /**
         * 当消息项被隐藏时回调
         * @param position 位置
         * @param messageObj 消息对象
         */
        fun onHideItem(position: Int, messageObj: MessageListEntity)

        /**
         * 当消息项被删除时回调
         * @param position 位置
         * @param messageObj 消息对象
         */
        fun onDeleteItem(position: Int, messageObj: MessageListEntity)

        /**
         * 当消息项被置顶/取消置顶时回调
         * @param position 位置
         * @param messageObj 消息对象
         * @param isPinned 是否置顶
         */
        fun onPinItem(position: Int, messageObj: MessageListEntity, isPinned: Boolean)
    }

    /**
     * 未读消息总数变化监听器
     */
    interface OnUnreadCountChangeListener {
        /**
         * 当未读消息总数变化时回调
         * @param totalUnreadCount 总未读消息数
         */
        fun onUnreadCountChanged(totalUnreadCount: Int)
    }

    private var mItemListener: OnMessageItemClickListener? = null
    private var mUnreadCountListener: OnUnreadCountChangeListener? = null

    /**
     * 设置消息项点击监听器
     * @param listener 监听器
     */
    fun setOnMessageItemClickListener(listener: OnMessageItemClickListener) {
        this.mItemListener = listener
    }

    /**
     * 设置未读消息总数变化监听器
     * @param listener 监听器
     */
    fun setOnUnreadCountChangeListener(listener: OnUnreadCountChangeListener) {
        this.mUnreadCountListener = listener
    }

    /**
     * 重写submitList方法，在数据更新时计算未读消息总数
     */
    override fun submitList(list: List<MessageListEntity>?) {
        super.submitList(list)
        // 计算未读消息总数
        val totalUnreadCount = list?.sumOf { it.unreadCount } ?: 0
        // 通知监听器
        mUnreadCountListener?.onUnreadCountChanged(totalUnreadCount)
    }

    /**
     * 重写submitList方法（带回调）
     */
    override fun submitList(list: List<MessageListEntity>?, commitCallback: Runnable?) {
        super.submitList(list, commitCallback)
        // 计算未读消息总数
        val totalUnreadCount = list?.sumOf { it.unreadCount } ?: 0
        // 通知监听器
        mUnreadCountListener?.onUnreadCountChanged(totalUnreadCount)
    }

    override fun getItemViewType(position: Int): Int {
        return if (!getItem(position).isBottomView) {
            VIEW_TYPE_MESSAGE
        } else {
            VIEW_TYPE_BOTTOM
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        // popupView inflate
        mPopupView = mInflater.inflate(R.layout.layout_msg_item_menu, null)
        // 计算高宽
        mMenuHight = mPopupView?.measuredHeight ?: 0
        mMenuWidth = mPopupView?.measuredWidth ?: 0

        return when (viewType) {
            VIEW_TYPE_MESSAGE -> {
                MsgListHolder(
                    ItemMsgVpBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false
                    )
                )
            }
            VIEW_TYPE_BOTTOM -> {
                BottomViewHolder(
                    ItemListBottomBinding.inflate(
                        LayoutInflater.from(parent.context),
                        parent,
                        false
                    )
                )
            }
            else -> throw IllegalArgumentException("Unknown view type: $viewType")
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        when (holder) {
            is MsgListHolder -> {
                val messageObj = getItem(position)
                holder.setData(messageObj, position)
            }
            is BottomViewHolder -> {
                holder.bind("Bottom")
            }
        }
    }

    /**
     * 清理资源，避免内存泄漏
     */
    fun release() {
        dismissMenu()
        mItemListener = null
        mUnreadCountListener = null
    }

    /**
     * 关闭当前菜单
     */
    fun dismissMenu() {
        mPopupWindow?.dismiss()
        mPopupWindow = null
        Timber.tag("dsc--").d("PopupMenu dismissed")
    }

    /**
     * 底部ViewHolder
     */
    inner class BottomViewHolder(private val binding: ItemListBottomBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bind(text: String) {
            binding.tvBottomText.text = "Bottom"
        }
    }

    /**
     * diffCallback
     * 用于比较两个项是否相同
     */
    private class DiffCallback : DiffUtil.ItemCallback<MessageListEntity>() {
        override fun areItemsTheSame(oldItem: MessageListEntity, newItem: MessageListEntity): Boolean {
            return oldItem.userId == newItem.userId
        }

        override fun areContentsTheSame(oldItem: MessageListEntity, newItem: MessageListEntity): Boolean {
            return oldItem == newItem
        }
    }

    inner class MsgListHolder(private val binding: ItemMsgVpBinding) :
        RecyclerView.ViewHolder(binding.root) {

        /**
         * 设置数据
         * @param obj 消息对象
         * @param position 位置
         */
        fun setData(obj: MessageListEntity, position: Int) {
            with(binding) {
                // 设置用户名和消息
                tvUsername.text = obj.userName
                // 根据消息类型不同，显示不同文本
                setupLastMessage(obj)

                if(!obj.isSysService()) {

                    // 时间
                    tvTimestamp.text = obj.timestamp
                    tvTimestamp.isVisible = true

                    // 设置未读消息数
                    setupUnreadCount(obj.unreadCount)


                    var status = UserInfoManager.getCachedStatus(obj.userId) ?: obj.onlineStatus
                    // 适配审核模式
                    if (StrategyManager.isReviewPkg()) {
                        getUserInfo(obj.userId) { userInfo ->
                            if (userInfo?.isAnswer == true && !StrategyManager.reviewPkgUsers.contains(userInfo.userId)) {
                                status = CallStatus.ONLINE
                            }else {
                                status = GlobalManager.getReviewOtherStatus(userInfo?.userId)
                            }
                        }
                    }

                    // 设置在线状态
                    setupOnlineStatus(status)

                }else {
                    binding.viewOnlineStatus.isVisible = false
                    tvTimestamp.isVisible = false
                    tvUnreadCount.isVisible = false
                }

                // 设置置顶状态背景
                updateItemBackground(obj.isPinned)

                // 加载头像
                loadAvatar(obj.avatarThumbUrl.ifEmpty { obj.avatar },obj)

                // 设置点击和长按事件
                setupClickListeners(obj, position)
            }
        }

        /**
         * 根据消息类型不同，显示不同文本
         */
        private fun setupLastMessage(msg: MessageListEntity){
            binding.tvLastMessage.setTextColor(
                ContextCompat.getColor(
                    binding.root.context,
                    if (msg.unreadCount > 0 && !msg.isSysService()) R.color.msg_unread else R.color.text_gray
                )
            )
            binding.tvLastMessage.text = when(msg.lastMessageType){
                MessageType.TEXT -> msg.lastMessage
                MessageType.SYSTEM->  _strSysService
                MessageType.IMAGE -> _strImage
                MessageType.LINK -> _strLink
                MessageType.VOICE -> _strVoice
                MessageType.GIFT -> _strGift
                MessageType.FILE -> _strFile
                MessageType.VIDEO -> _strVideo
                MessageType.CARD -> _strRechargeCard
                else -> ""
            }
        }

        private fun setupUnreadCount(unreadCount: Int) {
            if (unreadCount > 0) {
                binding.tvUnreadCount.apply {
                    visibility = View.VISIBLE
                    text = if (unreadCount > 99) "99+" else unreadCount.toString()
                }
            } else {
                binding.tvUnreadCount.visibility = View.GONE
            }
        }

        private fun setupOnlineStatus(status: String) {
            val statusBackground = when (status) {
                CallStatus.ONLINE,CallStatus.AVAILABLE -> R.drawable.shape_status_online
                CallStatus.BUSY -> R.drawable.shape_status_busy
                CallStatus.IN_CALL -> R.drawable.shape_status_in_call
                CallStatus.OFFLINE -> R.drawable.shape_status_offline
                CallStatus.UNKNOWN -> null
                else -> null
            }

            statusBackground?.let {
                binding.viewOnlineStatus.setBackgroundResource(it)
                binding.viewOnlineStatus.visibility = View.VISIBLE
            }?: {
                binding.viewOnlineStatus.visibility = View.GONE
            }
        }

        private fun loadAvatar(avatarUrl: String,obj: MessageListEntity) {
            if(obj.isSysService()){
                // 客服账号
                GlideUtils.load(
                    view = binding.ivAvatar,
                    url = null,
                    placeholder = R.drawable.customer_service,
                )
                return
            }
            GlideUtils.load(
                view =binding.ivAvatar,
                url = avatarUrl,
                placeholder = R.drawable.placeholder,
                error = R.drawable.placeholder,
            )
        }

        private fun setupClickListeners(obj: MessageListEntity, position: Int) {
            if (!obj.isSysService()) {
                // 头像点击
                ClickUtils.setOnGlobalDebounceClickListener(binding.ivAvatar) {
                    mItemListener?.onItemAvatarClick(position, obj)
                }
            }

            binding.root.apply {
                ClickUtils.setOnGlobalDebounceClickListener(this) {
                    mItemListener?.onItemClick(position, obj)
                }

                if(!obj.isSysService()) {
                    // 触摸事件，记录长按位置
                    setOnTouchListener { _, event ->
                        when (event.action) {
                            MotionEvent.ACTION_DOWN -> {
                                mTouchPoint.x = event.rawX.toInt()
                                mTouchPoint.y = event.rawY.toInt()
                            }
                        }
                        // 不消费事件，继续传递
                        false
                    }

                    // 长按事件
                    setOnLongClickListener {
                        // 使用ClickUtils防抖，避免频繁弹出菜单
                        if (!ClickUtils.isFastClickGlobal()) {
                            Timber.tag("dsc--").d("Message item long clicked: ${obj.userName}")
                            showPowerMenu(context, it, obj, position)
                        } else {
                            Timber.tag("dsc--").d("Fast long click blocked: ${obj.userName}")
                        }
                        true
                    }
                }
            }
        }

        /**
         * 更新项目背景
         * @param isPinned 是否置顶
         */
        private fun updateItemBackground(isPinned: Boolean) {
            binding.root.setBackgroundResource(
                if (isPinned) R.drawable.bg_message_item_pinned
                else R.drawable.bg_mine_function_item_selector
            )
        }

        /**
         * 使用PowerMenu显示弹出菜单
         * @param context 上下文
         * @param anchorView 锚点视图
         * @param messageObj 消息对象
         * @param position 位置
         */
        private fun showPowerMenu(
            context: Context,
            anchorView: View,
            messageObj: MessageListEntity,
            position: Int
        ) {
            // 关闭之前的菜单
            dismissMenu()
            Timber.tag("dsc--").d("Showing menu for item: ${messageObj.userName}")

            // 点击事件
            mPopupView?.click {

            }
            mPopupView?.apply {
                // 使用独立的点击监听器，不共享防抖时间戳，每个菜单项独立计时
                // hiden
                ClickUtils.setOnIsolatedClickListener(findViewById(R.id.layout_msg_item_menu_hide)) {
                    handleMenuItemClick(_strHiden, messageObj, position)
                }
                
                // delete
                ClickUtils.setOnIsolatedClickListener(findViewById(R.id.layout_msg_item_menu_delete)) {
                    handleMenuItemClick(_strDelete, messageObj, position)
                }
                
                // pin or unpin
                val pinButton = findViewById<TextView>(R.id.tv_msg_item_menu_pin)
                val pinButtonImage = findViewById<View>(R.id.image_flag)
                pinButton.text = if (messageObj.isPinned) _strUnpin else _strPin
                pinButtonImage.setBackgroundResource(if (messageObj.isPinned) R.drawable.unpin else R.drawable.pin)
                ClickUtils.setOnIsolatedClickListener(findViewById(R.id.layout_msg_item_menu_pin)) {
                    val actionText = pinButton.text.toString()
                    handleMenuItemClick(actionText, messageObj, position)
                }
            }

            // popup设置
            mPopupWindow = PopupWindow(
                mPopupView,
                ViewGroup.LayoutParams.WRAP_CONTENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
            ).apply {
                isOutsideTouchable = true
                isFocusable = true
            }


            // 显示菜单
            if (mTouchPoint.x <= _screenWidth / 2) {
                if (mTouchPoint.y + mMenuHight < _screenHight) {
                    mPopupWindow?.showAtLocation(
                        anchorView,
                        ANCHORED_GRAVITY,
                        mTouchPoint.x,
                        mTouchPoint.y + VERTICAL_OFFSET
                    )
                } else {
                    mPopupWindow?.showAtLocation(
                        anchorView,
                        ANCHORED_GRAVITY,
                        mTouchPoint.x,
                        mTouchPoint.y - mMenuHight - VERTICAL_OFFSET
                    )
                }
            } else {
                if (mTouchPoint.y + mMenuHight < _screenHight) {
                    mPopupWindow?.showAtLocation(
                        anchorView,
                        ANCHORED_GRAVITY,
                        mTouchPoint.x - mMenuWidth,
                        mTouchPoint.y + VERTICAL_OFFSET
                    )
                } else {
                    mPopupWindow?.showAtLocation(
                        anchorView,
                        ANCHORED_GRAVITY,
                        mTouchPoint.x - mMenuWidth,
                        mTouchPoint.y - mMenuHight - VERTICAL_OFFSET
                    )
                }
            }
        }

        /**
         * 处理菜单项点击事件
         */
        private fun handleMenuItemClick(
            title: String,
            messageObj: MessageListEntity,
            position: Int
        ) {
            when (title) {
                _strHiden -> mItemListener?.onHideItem(position, messageObj)
                _strDelete -> mItemListener?.onDeleteItem(position, messageObj)
                _strPin, _strUnpin -> {
                    val newPinnedState = !messageObj.isPinned

                    // 立即更新UI背景
                    updateItemBackground(newPinnedState)

                    // 更新数据并通知监听器
                    messageObj.isPinned = newPinnedState
                    mItemListener?.onPinItem(position, messageObj, newPinnedState)
                }
            }
            mPopupWindow?.dismiss()
        }
    }
}