package com.score.callmetest.util

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.app.Application
import android.os.Build
import java.lang.ref.WeakReference
import com.score.callmetest.manager.AppLifecycleManager
import androidx.core.net.toUri
import timber.log.Timber

/**
 * Activity工具类，提供页面跳转与相关辅助方法
 */
object ActivityUtils {
    /**
     * 跳转到指定Activity
     * @param context 上下文
     * @param targetClass 目标Activity类
     * @param bundle 可选参数，传递数据
     * @param finish 是否关闭当前Activity
     */
    @JvmStatic
    fun <T : Activity> startActivity(
        context: Context,
        targetClass: Class<T>,
        bundle: Bundle? = null,
        finish: Boolean = false
    ) {
        val intent = Intent(context, targetClass)
        bundle?.let { intent.putExtras(it) }
        context.startActivity(intent)
        if (finish && context is Activity) {
            context.finish()
        }
    }

    /**
     * 跳转到指定Activity并清空任务栈
     */
    @JvmStatic
    fun <T : Activity> startActivityClearTask(
        context: Context,
        targetClass: Class<T>,
        bundle: Bundle? = null
    ) {
        val intent = Intent(context, targetClass)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        bundle?.let { intent.putExtras(it) }
        context.startActivity(intent)
    }

    /**
     * 跳转到指定Activity并等待结果
     */
    @JvmStatic
    fun <T : Activity> startActivityForResult(
        activity: Activity,
        targetClass: Class<T>,
        requestCode: Int,
        bundle: Bundle? = null
    ) {
        val intent = Intent(activity, targetClass)
        bundle?.let { intent.putExtras(it) }
        activity.startActivityForResult(intent, requestCode)
    }

    /**
     * 关闭当前Activity
     */
    @JvmStatic
    fun finish(activity: Activity) {
        activity.finish()
    }

    /**
     * 关闭当前Activity并带结果返回
     */
    @JvmStatic
    fun finishWithResult(activity: Activity, resultCode: Int, data: Intent? = null) {
        activity.setResult(resultCode, data)
        activity.finish()
    }

    /**
     * 获取当前最顶层Activity
     */
    @JvmStatic
    fun getTopActivity(): Activity? {
        return AppLifecycleManager.getTopActivity()
    }

    /**
     * 打开外部网页
     * @param [activity] 活动
     * @param [h5Url] h5 url
     */
    fun openExternalWeb(activity: Activity, h5Url: String) {
        try {
            val intent = Intent(Intent.ACTION_VIEW, h5Url.toUri())
            activity.startActivity(intent)
        } catch (e: Exception) {
            Timber.e(e)
        }
    }
} 