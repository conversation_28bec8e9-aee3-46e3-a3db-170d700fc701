package com.score.callmetest.util

import android.annotation.SuppressLint
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import java.util.concurrent.TimeUnit

object TimeUtils {


    /**
     * 格式化时间戳
     *
     * <1天（显示hh:mm）
     * 大于1天（显示MM-dd hh:mm）
     * 非今年消息（yyyy-MM-DD hh:mm）
     */
    fun formatTimestampForChatList(timestamp: Long): String {
        val calendar = Calendar.getInstance()
        val currentYear = calendar.get(Calendar.YEAR)
        val currentDay = calendar.get(Calendar.DAY_OF_YEAR)

        val date = Date(timestamp)
        val messageCalendar = Calendar.getInstance().apply {
            time = date
        }
        val messageYear = messageCalendar.get(Calendar.YEAR)
        val messageDay = messageCalendar.get(Calendar.DAY_OF_YEAR)

        return when {
            // 今天内的消息，显示时分
            currentYear == messageYear && currentDay == messageDay -> {
                SimpleDateFormat("HH:mm", Locale.getDefault()).format(date)
            }
            // 非今年的消息，显示年月日时分
            currentYear != messageYear -> {
                SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault()).format(date)
            }
            // 今年内但不是今天的消息，显示月日时分
            else -> {
                SimpleDateFormat("MM-dd HH:mm", Locale.getDefault()).format(date)
            }
        }
    }

    /**
     * 获取格式化的时间
     * @return 格式化的时间字符串
     */
    fun getFormattedTime(timestamp: Long): String {
        val now = System.currentTimeMillis()
        val date = Date(timestamp)
        val sdf = when {
            isToday(timestamp, now) -> SimpleDateFormat("HH:mm", Locale.getDefault())
            isYesterday(timestamp, now) -> return "昨天"
            isThisYear(timestamp, now) -> SimpleDateFormat("MM-dd", Locale.getDefault())
            else -> SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        }
        return sdf.format(date)
    }

    /**
     * 判断是否是今天
     */
    private fun isToday(timestamp: Long, now: Long): Boolean {
        val sdf = SimpleDateFormat("yyyyMMdd", Locale.getDefault())
        return sdf.format(timestamp) == sdf.format(now)
    }

    /**
     * 判断是否是昨天
     */
    private fun isYesterday(timestamp: Long, now: Long): Boolean {
        val sdf = SimpleDateFormat("yyyyMMdd", Locale.getDefault())
        val yesterdayDate = Date(now - 24 * 60 * 60 * 1000)
        return sdf.format(timestamp) == sdf.format(yesterdayDate)
    }

    /**
     * 判断是否是今年
     */
    private fun isThisYear(timestamp: Long, now: Long): Boolean {
        val sdf = SimpleDateFormat("yyyy", Locale.getDefault())
        return sdf.format(timestamp) == sdf.format(now)
    }



    /**
     * 工具方法：将秒数转换为 hh:mm:ss 格式的时间字符串
     *
     * @param seconds 输入的秒数
     * @return 格式化后的时间字符串，格式为 hh:mm:ss
     */
    @SuppressLint("DefaultLocale")
    fun formatSecondsToTime(seconds: Long): String {
        val h = seconds / 3600
        val m = (seconds % 3600) / 60
        val s = seconds % 60
        return String.format("%02d:%02d:%02d", h, m, s)
    }

    /**
     * 将毫秒数格式化为 hh:mm:ss 格式的时间字符串
     *
     * @param milliseconds 输入的毫秒数
     * @return 格式化后的时间字符串，格式为 hh:mm:ss
     */
    @SuppressLint("DefaultLocale")
    fun formatTime(milliseconds: Long): String {
        val hours = TimeUnit.MILLISECONDS.toHours(milliseconds)
        val minutes = TimeUnit.MILLISECONDS.toMinutes(milliseconds) % 60
        val seconds = TimeUnit.MILLISECONDS.toSeconds(milliseconds) % 60
        return String.format("%02d:%02d:%02d", hours, minutes, seconds)
    }

    /**
     * 将秒数格式化为可读的时间格式（如：1h2m3s）
     *
     * @param seconds 输入的秒数
     * @return 格式化后的时间字符串，格式为 XhYmZs（根据时长自动省略不需要的部分）
     */
    fun formatDurationToReadable(seconds: Int): String {
        if (seconds < 0) return "0s"

        val hours = seconds / 3600
        val minutes = (seconds % 3600) / 60
        val remainingSeconds = seconds % 60

        val result = StringBuilder()

        if (hours > 0) {
            result.append("${hours}h")
        }

        if (minutes > 0) {
            result.append("${minutes}m")
        }

        result.append("${remainingSeconds}s")

        return result.toString()
    }
}