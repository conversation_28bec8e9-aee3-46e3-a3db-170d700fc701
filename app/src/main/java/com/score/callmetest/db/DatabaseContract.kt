package com.score.callmetest.db

import com.score.callmetest.entity.CallHistoryEntity
import com.score.callmetest.entity.ChatMessageEntity
import com.score.callmetest.entity.MessageListEntity
import kotlinx.coroutines.flow.Flow

/**
 * 数据库操作接口，定义了数据库的所有操作
 * 该接口作为数据库操作的抽象，方便后续替换Room为其他数据库实现
 */
interface DatabaseContract {
    
    // <editor-folder desc="消息列表相关操作">
    
    /**
     * 插入一条消息列表项
     * @param messageList 消息列表项实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun insertMessageList(messageList: MessageListEntity, callback: ((Boolean) -> Unit)? = null)
    
    /**
     * 批量插入消息列表项
     * @param messageLists 消息列表项实体列表
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun insertMessageLists(messageLists: List<MessageListEntity>, callback: ((Boolean) -> Unit)? = null)

    /**
     * 更新当前用户的消息列表项
     * @param messageList 消息列表项实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun updateCurrentUserMessageList(messageList: MessageListEntity, callback: ((Boolean) -> Unit)? = null)
    
    /**
     * 删除一条消息列表项
     * @param messageList 消息列表项实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun deleteMessageList(messageList: MessageListEntity, callback: ((Boolean) -> Unit)? = null)
    
    /**
     * 根据用户ID删除一条消息列表项
     * @param userId 用户ID
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun deleteMessageListById(userId: String, callback: ((Boolean) -> Unit)? = null)

    /**
     * 获取当前用户的所有消息列表项，按是否置顶和时间排序
     * @return 消息列表项流
     */
    fun getCurrentUserAllMessageLists(): Flow<List<MessageListEntity>>

    /**
     * 根据用户ID获取一条消息列表项
     * @param userId 用户ID
     * @param callback 回调函数，在操作成功或失败时调用，参数为消息列表项实体或null
     */
    fun getMessageListById(userId: String, callback: (MessageListEntity?) -> Unit)
    
    /**
     * 更新未读消息数
     * @param userId 用户ID
     * @param unreadCount 未读消息数
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun updateUnreadCount(userId: String, unreadCount: Int, callback: ((Boolean) -> Unit)? = null)
    
    /**
     * 更新是否置顶
     * @param userId 用户ID
     * @param isPinned 是否置顶
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun updatePinStatus(userId: String, isPinned: Boolean, callback: ((Boolean) -> Unit)? = null)

    /**
     * 更新状态
     * @param userId 用户ID
     * @param status 状态
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun updateStatus(userId: String, status: String, callback: ((Boolean) -> Unit)? = null)

    /**
     * 清空当前用户的所有消息列表
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun clearCurrentUserAllMessageLists(callback: ((Boolean) -> Unit)? = null)

    // </editor-folder>

    // <editor-folder desc="通话历史相关操作">

    /**
     * 插入一条通话历史
     * @param callHistory 通话历史实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun insertCallHistory(callHistory: CallHistoryEntity, callback: ((Boolean) -> Unit)? = null)
    
    /**
     * 批量插入通话历史
     * @param callHistories 通话历史实体列表
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun insertCallHistories(callHistories: List<CallHistoryEntity>, callback: ((Boolean) -> Unit)? = null)
    


    /**
     * 更新当前用户的通话历史
     * @param callHistory 通话历史实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun updateCurrentUserCallHistory(callHistory: CallHistoryEntity, callback: ((Boolean) -> Unit)? = null)
    
    /**
     * 删除一条通话历史
     * @param callHistory 通话历史实体
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun deleteCallHistory(callHistory: CallHistoryEntity, callback: ((Boolean) -> Unit)? = null)
    
    /**
     * 根据ID删除一条通话历史
     * @param id 通话历史ID
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun deleteCallHistoryById(id: String, callback: ((Boolean) -> Unit)? = null)
    

    /**
     * 获取当前用户的所有通话历史，按时间戳倒序排序
     * @return 通话历史流
     */
    fun getCurrentUserAllCallHistories(): Flow<List<CallHistoryEntity>>

    /**
     * 获取特定用户的通话历史，按时间戳倒序排序
     * @param userId 用户ID
     * @return 通话历史流
     */
    fun getCallHistoriesByUserId(userId: String): Flow<List<CallHistoryEntity>>
    
    /**
     * 根据ID获取一条通话历史
     * @param id 通话历史ID
     * @param callback 回调函数，在操作成功或失败时调用，参数为通话历史实体或null
     */
    fun getCallHistoryById(id: String, callback: (CallHistoryEntity?) -> Unit)
    


    /**
     * 清空当前用户的所有通话历史
     * @param callback 回调函数，在操作成功或失败时调用
     */
    fun clearCurrentUserAllCallHistories(callback: ((Boolean) -> Unit)? = null)


    /**
     * 清空当前用户的所有数据
     */
    fun clearCurrentUserDatas(callback: ((Boolean) -> Unit)? = null)

    // </editor-folder>
} 