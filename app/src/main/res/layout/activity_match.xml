<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/match_bg">

    <com.opensource.svgaplayer.SVGAImageView
        android:id="@+id/match_icon"
        android:layout_width="200dp"
        android:layout_height="200dp"
        android:layout_marginTop="227dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/match_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="60dp"
        android:textColor="@android:color/white"
        android:textSize="17sp"
        android:textStyle="bold"
        android:typeface="sans"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/match_icon" />

    <com.score.callmetest.ui.widget.AlphaImageView
        android:id="@+id/close"
        android:layout_width="56dp"
        android:layout_height="56dp"
        android:layout_marginBottom="71dp"
        android:src="@drawable/match_close"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
