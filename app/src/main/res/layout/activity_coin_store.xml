<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="#000">
    <Space
        android:id="@+id/space_top"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        app:layout_constraintTop_toTopOf="parent"/>


    <!-- 顶部栏：返回按钮、标题 -->
    <LinearLayout
        android:id="@+id/llTopBar"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:paddingHorizontal="15dp"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        app:layout_constraintTop_toBottomOf="@+id/space_top"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:id="@+id/ivBack"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/info_btn_back" />

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="@string/str_store"
            android:textColor="#fff"
            android:textSize="18sp"
            android:textStyle="bold"
            android:gravity="center" />


        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="13dp"
            android:layout_height="13dp"
            android:src="@drawable/coin" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_coin_balance"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="3dp"
            android:fontFamily="@font/roboto_medium"
            android:textColor="@android:color/white"
            android:textSize="14sp"
            android:textStyle="bold"
            tools:text="30000" />
    </LinearLayout>

    <!-- 我的金币 和 支付方式选择 在同一行 -->
    <LinearLayout
        android:id="@+id/llTopRow"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:baselineAligned="false"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/llTopBar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="22dp"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp">

        <!-- 我的金币 -->
        <LinearLayout
            android:id="@+id/llMyCoins"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <TextView
                android:id="@+id/tvMyCoins"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="My coins:"
                android:textColor="#ffffffff"
                android:textSize="15sp"/>
        </LinearLayout>


        <!-- 右侧：支付方式选择器 -->
        <LinearLayout
            android:id="@+id/llRightColumn"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:visibility="gone"
            tools:visibility="visible"
            android:gravity="end">

            <!-- 支付方式选择 -->
            <LinearLayout
                android:id="@+id/paymentMethodSelector"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="10dp"
                android:paddingVertical="2dp">

                <ImageView
                    android:id="@+id/ivPaymentMethod"
                    android:layout_width="19dp"
                    android:layout_height="19dp"
                    android:src="@drawable/ic_google"
                    android:layout_marginEnd="4dp"/>

                <TextView
                    android:id="@+id/tvPaymentMethod"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Google Play"
                    android:textColor="#ffffffff"
                    android:textSize="13sp"/>

                <FrameLayout
                    android:layout_width="15dp"
                    android:layout_height="22dp">

                    <ImageView
                        android:id="@+id/ivPaymentArrow"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="2dp"
                        android:layout_gravity="center_vertical|start"
                        android:src="@drawable/btn_down_arrow"/>

                    <View
                        android:id="@+id/paymentMethodRedDot"
                        android:layout_width="6dp"
                        android:layout_height="6dp"
                        android:background="@drawable/bg_tag_red"
                        android:visibility="visible"
                        android:layout_gravity="top|end"
                        android:layout_marginEnd="0dp"
                        android:translationY="0dp"/>
                </FrameLayout>
            </LinearLayout>


        </LinearLayout>
    </LinearLayout>

    <!-- 折扣标签 -->
    <LinearLayout
        android:id="@+id/line_payment_discount"
        android:layout_width="50dp"
        android:layout_height="16dp"
        android:layout_marginTop="8dp"
        android:layout_marginEnd="36dp"
        android:background="@drawable/bg_promotion_tag"
        android:gravity="center"
        android:visibility="gone"
        tools:visibility="visible"
        android:orientation="horizontal"
        android:layout_marginBottom="-3dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/llTopRow">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:layout_marginEnd="1dp"
            android:src="@drawable/coin" />

        <TextView
            android:id="@+id/tvPaymentBonus"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="+50%"
            android:textColor="#fff"
            android:textSize="10sp"
            android:textStyle="bold" />
    </LinearLayout>

    <!-- 充值套餐列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rvRechargeOptions"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="24dp"
        android:layout_marginBottom="16dp"
        app:layout_constraintBottom_toTopOf="@id/btnCustomerService"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/llTopRow"
        app:layout_constraintVertical_bias="0.0" />
    <!-- 客服按钮 -->
    <com.score.callmetest.ui.widget.AlphaLinearLayout
        android:id="@+id/btnCustomerService"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginBottom="50dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/customer"
            android:layout_marginEnd="4dp"
            app:tint="#fff" />
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Customer Service"
            android:textColor="#fff"
            android:textSize="13sp"/>
    </com.score.callmetest.ui.widget.AlphaLinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>