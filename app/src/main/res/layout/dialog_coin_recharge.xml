<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_gift_bottomsheet"
    android:paddingBottom="15dp"
    android:paddingTop="20dp"
    android:orientation="vertical">

    <!-- 顶部金币数量 -->
    <TextView
        android:id="@+id/tv_coin_balance"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="17dp"
        android:drawableLeft="@drawable/coin"
        android:padding="8dp"
        android:text=" 1000"
        android:textColor="@android:color/white"
        android:textSize="18sp" />

    <com.score.callmetest.ui.widget.AlphaImageView
        android:id="@+id/coin_service"
        android:layout_width="25dp"
        android:layout_height="25dp"
        android:layout_alignTop="@+id/tv_coin_balance"
        android:layout_alignBottom="@+id/tv_coin_balance"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="15dp"
        android:src="@drawable/service" />

    <com.score.callmetest.ui.widget.RechargeSubView
        android:id="@+id/recharge_sub_view"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/tv_coin_balance"/>
</RelativeLayout>