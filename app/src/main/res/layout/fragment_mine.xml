<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F5F5FA">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="match_parent"
            android:layout_height="189dp"
            android:background="@drawable/main_bg" />

        <!-- 右上角按钮 -->
        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/btn_edit"
            android:layout_width="22dp"
            android:layout_height="22dp"
            android:layout_alignParentEnd="true"
            android:layout_marginTop="53dp"
            android:layout_marginEnd="25dp"
            android:src="@drawable/edit" />

        <!-- 头像 -->
        <com.score.callmetest.ui.widget.CircleIconButton
            android:id="@+id/iv_avatar"
            android:layout_width="68dp"
            android:layout_height="68dp"
            android:layout_marginStart="15dp"
            android:layout_marginTop="68dp"
            app:bgColor="@android:color/white"
            app:iconSrc="@drawable/placeholder" />


        <!-- 用户名和装饰 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignTop="@+id/iv_avatar"
            android:layout_alignBottom="@+id/iv_avatar"
            android:layout_marginStart="8dp"
            android:layout_toEndOf="@id/iv_avatar"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:id="@+id/tv_nickname"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="CCHUBBY"
                android:textColor="@color/black"
                android:textSize="20sp"
                android:textStyle="bold" />

        </LinearLayout>


        <!-- 关注/粉丝卡片 -->
        <androidx.cardview.widget.CardView
            android:id="@+id/fans_card"
            android:layout_width="match_parent"
            android:layout_height="78dp"
            android:layout_below="@+id/iv_avatar"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            app:cardCornerRadius="16dp"
            app:cardElevation="2dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:gravity="center"
                android:orientation="horizontal">

                <com.score.callmetest.ui.widget.AlphaLinearLayout
                    android:id="@+id/following_card"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_following"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:includeFontPadding="false"
                        android:text="0"
                        android:textColor="@color/black"
                        android:textSize="17sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:includeFontPadding="false"
                        android:text="Following"
                        android:textColor="#888888"
                        android:textSize="13sp" />
                </com.score.callmetest.ui.widget.AlphaLinearLayout>

                <View
                    android:layout_width="1dp"
                    android:layout_height="32dp"
                    android:background="#EEEEEE" />

                <com.score.callmetest.ui.widget.AlphaLinearLayout
                    android:id="@+id/follower_card"
                    android:layout_width="0dp"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:gravity="center"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tv_followers"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:includeFontPadding="false"
                        android:text="0"
                        android:textColor="@color/black"
                        android:textSize="17sp"
                        android:textStyle="bold" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:includeFontPadding="false"
                        android:text="Followers"
                        android:textColor="#888888"
                        android:textSize="13sp" />
                </com.score.callmetest.ui.widget.AlphaLinearLayout>
            </LinearLayout>
        </androidx.cardview.widget.CardView>

        <!-- 功能列表 -->
        <com.score.callmetest.ui.widget.AlphaRelativeLayout
            android:id="@+id/coin_card"
            android:layout_width="match_parent"
            android:layout_height="66dp"
            android:layout_below="@+id/fans_card"
            android:layout_marginStart="15dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="15dp"
            android:background="#FFF5D9">

            <ImageView
                android:layout_width="19dp"
                android:layout_height="19dp"
                android:layout_centerVertical="true"
                android:layout_marginStart="16dp"
                android:src="@drawable/coin" />

            <TextView
                android:id="@+id/tv_my_coins"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerVertical="true"
                android:layout_marginStart="43dp"
                android:paddingBottom="2dp"
                android:text="My Coins"
                android:textColor="@color/black"
                android:textSize="14sp"
                android:textStyle="bold"
                android:typeface="sans" />

            <TextView
                android:id="@+id/tv_coin_value"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="40dp"
                android:gravity="end"
                android:text="200"
                android:textColor="#FF712C"
                android:textSize="15sp"
                android:textStyle="bold" />

            <ImageView
                android:layout_width="6dp"
                android:layout_height="8dp"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="25dp"
                android:src="@drawable/triangle" />
        </com.score.callmetest.ui.widget.AlphaRelativeLayout>

        <LinearLayout
            android:id="@+id/line_function"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@+id/coin_card"
            android:layout_marginStart="8dp"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="8dp"
            android:orientation="vertical"
            android:background="@android:color/white">

            <com.score.callmetest.ui.widget.MineFunctionItemView
                android:id="@+id/language"
                android:layout_width="match_parent"
                android:layout_height="54dp"
                app:mfi_icon="@drawable/language"
                android:visibility="gone"
                app:mfi_title="Language" />

            <com.score.callmetest.ui.widget.MineFunctionItemView
                android:id="@+id/customer_service"
                android:layout_width="match_parent"
                android:layout_height="54dp"
                app:mfi_icon="@drawable/customer"
                app:mfi_title="Customer Service" />

            <com.score.callmetest.ui.widget.MineFunctionItemView
                android:id="@+id/settings"
                android:layout_width="match_parent"
                android:layout_height="54dp"
                app:mfi_icon="@drawable/settings"
                app:mfi_title="Settings" />
        </LinearLayout>
    </RelativeLayout>
</ScrollView> 