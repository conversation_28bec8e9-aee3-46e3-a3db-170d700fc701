<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_dialog_rounded">

        <ImageView
            android:id="@+id/dialog_bg"
            android:layout_width="match_parent"
            android:layout_height="90dp"
            android:background="@drawable/dialog_bg_green" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingHorizontal="32dp"
            android:paddingTop="25dp"
            android:paddingBottom="25dp">


            <TextView
                android:id="@+id/title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="Random Match"
                android:textColor="@color/black"
                android:textSize="19sp"
                android:textStyle="bold" />

            <TextView
                android:id="@+id/content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:fontFamily="@font/roboto_medium"
                android:gravity="center"
                android:lineHeight="18dp"
                android:text="You have 3 free calls to use!"
                android:textColor="#6E7274"
                android:textSize="15sp"
                android:textStyle="normal" />

            <com.opensource.svgaplayer.SVGAImageView
                android:id="@+id/avatars_svga"
                android:layout_width="match_parent"
                android:layout_marginHorizontal="25dp"
                android:layout_marginVertical="34dp"
                android:layout_height="92dp"/>

            <com.score.callmetest.ui.widget.AlphaLinearLayout
                android:id="@+id/btn_confirm_layout"
                android:layout_width="match_parent"
                android:layout_height="52dp"
                android:gravity="center"
                android:orientation="horizontal">

                <com.opensource.svgaplayer.SVGAImageView
                    android:id="@+id/video_svga"
                    android:layout_width="26dp"
                    android:layout_height="26dp"
                    android:layout_marginEnd="13dp"/>

                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/btn_confirm"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="Free Match"
                        android:textColor="#FFFFFF"
                        android:textSize="16sp"
                        android:textStyle="bold" />

                    <TextView
                        android:id="@+id/tv_tip"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:text="Free Match"
                        android:layout_marginTop="3dp"
                        android:textColor="#FFFFFF"
                        android:textSize="10sp" />
                </LinearLayout>

            </com.score.callmetest.ui.widget.AlphaLinearLayout>

        </LinearLayout>

        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/close"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:padding="5dp"
            android:layout_gravity="end"
            android:layout_marginEnd="10dp"
            android:layout_marginTop="10dp"
            android:src="@drawable/flash_dialog_close"/>
    </FrameLayout>
</FrameLayout>