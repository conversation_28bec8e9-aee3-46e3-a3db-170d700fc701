<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">

    <!-- 远端视频容器（背景） -->

    <FrameLayout
        android:id="@+id/remote_video_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.0">

    </FrameLayout>

    <!-- 对方信息   -->
    <LinearLayout
        android:id="@+id/info_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="13dp"
        android:layout_marginTop="41dp"
        android:layout_marginEnd="70dp"
        android:gravity="center_vertical|start"
        android:orientation="horizontal"
        android:paddingHorizontal="6dp"
        android:paddingVertical="3dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 对方头像 -->
        <com.score.callmetest.ui.widget.CircleIconButton
            android:id="@+id/iv_anchor_avatar"
            android:layout_width="38dp"
            android:layout_height="38dp"
            android:scaleType="centerCrop"
            android:src="@drawable/ic_launcher_background" />

        <TextView
            android:id="@+id/tv_anchor_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="6dp"
            android:layout_weight="1"
            android:ellipsize="end"
            android:gravity="start"
            android:maxLines="1"
            android:text="asfdew"
            android:textColor="@android:color/white"
            android:textSize="16sp"
            android:textStyle="bold" />

        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/iv_warning"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_marginStart="6dp"
            android:src="@drawable/report"
            android:visibility="gone" />

        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/iv_follow"
            android:layout_width="28dp"
            android:layout_height="28dp"
            android:layout_marginStart="5dp"
            android:src="@drawable/video_call_add" />
    </LinearLayout>

    <TextView
        android:id="@+id/call_duration_text"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="18dp"
        android:layout_marginTop="9dp"
        android:layout_marginEnd="144dp"
        android:gravity="center_vertical"
        android:text="After 35 S, you will spend 1600/min"
        android:textColor="@android:color/white"
        android:textSize="14sp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/info_layout" />

    <!-- 退出按钮   -->
    <com.score.callmetest.ui.widget.AlphaImageView
        android:id="@+id/iv_quit"
        android:layout_width="33dp"
        android:layout_height="33dp"
        android:layout_marginTop="50dp"
        android:layout_marginEnd="22dp"
        android:src="@drawable/video_btn_quit"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 本地视频小窗，使用clip实现12dp圆角 -->
    <FrameLayout
        android:id="@+id/local_video_container_parent"
        android:layout_width="94dp"
        android:layout_height="144dp"
        android:layout_marginTop="107dp"
        android:layout_marginEnd="24dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <FrameLayout
            android:id="@+id/local_video_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/bg_local_video_rounded"
            android:clipToOutline="true"
            android:outlineProvider="background" />
    </FrameLayout>

    <!-- 翻转摄像头 -->
    <com.score.callmetest.ui.widget.AlphaImageView
        android:id="@+id/camera"
        android:layout_width="37dp"
        android:layout_height="37dp"
        android:layout_marginBottom="-18dp"
        android:src="@drawable/camera"
        app:layout_constraintBottom_toBottomOf="@+id/local_video_container_parent"
        app:layout_constraintEnd_toEndOf="@+id/local_video_container_parent"
        app:layout_constraintStart_toStartOf="@+id/local_video_container_parent" />


    <!-- 底部输入栏，防止软键盘遮挡 -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/bottom_scroll_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:fillViewport="true"
        android:fitsSystemWindows="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/bottom_input_bar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="15dp"
                android:layout_marginEnd="15dp"
                android:layout_marginBottom="24dp"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <EditText
                    android:id="@+id/et_input"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:gravity="top|start"
                    android:hint="Type here..."
                    android:imeOptions="actionSend"
                    android:inputType="textMultiLine"
                    android:maxLines="3"
                    android:overScrollMode="always"
                    android:paddingVertical="7dp"
                    android:paddingStart="12dp"
                    android:paddingEnd="12dp"
                    android:scrollbars="vertical"
                    android:textColor="#FFF"
                    android:textColorHint="#B3FFFFFF"
                    android:textSize="16sp" />

                <com.score.callmetest.ui.widget.AlphaImageView
                    android:id="@+id/iv_send"
                    android:layout_width="42dp"
                    android:layout_height="42dp"
                    android:layout_marginStart="13dp"
                    android:background="@drawable/chat_send" />
            </LinearLayout>
        </LinearLayout>
    </androidx.core.widget.NestedScrollView>

    <!-- 金币充值  -->
    <com.score.callmetest.ui.widget.AlphaImageView
        android:id="@+id/video_coin"
        android:layout_width="58dp"
        android:layout_height="58dp"
        android:layout_marginEnd="13dp"
        android:layout_marginBottom="20dp"
        android:src="@drawable/video_coin"
        app:layout_constraintBottom_toTopOf="@+id/bottom_scroll_container"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 礼物  -->
    <com.score.callmetest.ui.widget.AlphaImageView
        android:id="@+id/video_gift"
        android:layout_width="58dp"
        android:layout_height="58dp"
        android:layout_marginEnd="13dp"
        android:layout_marginBottom="20dp"
        android:src="@drawable/video_gift"
        app:layout_constraintBottom_toTopOf="@+id/video_coin"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 标记礼物左侧位置,以防止礼物图标消失，手动计算71dp  -->
    <View
        android:id="@+id/gift_left_baseline"
        android:layout_width="1dp"
        android:layout_height="0dp"
        android:layout_marginEnd="71dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 消息列表 -->
    <com.score.callmetest.ui.widget.GradientRecyclerView
        android:id="@+id/rv_message_list"
        android:layout_width="0dp"
        android:layout_height="200dp"
        android:layout_marginStart="15dp"
        android:layout_marginBottom="10dp"
        android:overScrollMode="never"
        android:scrollbars="none"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/llCoinNotEnough"
        app:layout_constraintEnd_toStartOf="@id/gift_left_baseline"
        app:layout_constraintStart_toStartOf="parent"
        tools:itemCount="4"
        tools:listitem="@layout/item_video_msg"
        tools:visibility="visible" />

    <!-- 金币不足倒计时提示框 -->
    <FrameLayout
        android:id="@+id/llCoinNotEnough"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="13dp"
        android:layout_marginTop="100dp"
        android:layout_marginBottom="20dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@+id/bottom_scroll_container"
        app:layout_constraintEnd_toStartOf="@id/gift_left_baseline"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible">

        <LinearLayout
            android:id="@+id/coin_not_enough_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            android:layout_marginEnd="14dp"
            android:background="#B36B3A00"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:padding="10dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tvCoinNotEnoughTitle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Coins not enough"
                    android:textColor="#FFFD65"
                    android:textSize="16sp"
                    android:textStyle="bold" />

                <TextView
                    android:id="@+id/tvCoinCountdown"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Video duration remaining:60s"
                    android:textColor="#FFFFFFFF"
                    android:textSize="15sp" />
            </LinearLayout>

            <TextView
                android:id="@+id/btnRecharge"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:gravity="center"
                android:paddingHorizontal="10dp"
                android:paddingVertical="7dp"
                android:text="Recharge"
                android:textColor="#FFFFFFFF"
                android:textSize="13sp"
                android:textStyle="bold" />
        </LinearLayout>

        <com.score.callmetest.ui.widget.AlphaImageView
            android:id="@+id/ivCloseCoinNotEnough"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:layout_gravity="end"
            android:src="@drawable/ic_circle_close" />
    </FrameLayout>

    <!-- 索要礼物   -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_gift_request"
        android:layout_width="0dp"
        android:layout_height="36dp"
        android:layout_marginStart="15dp"
        android:layout_marginBottom="10dp"
        android:background="@drawable/bg_ask_gift_shape"
        android:paddingHorizontal="8dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/rv_message_list"
        app:layout_constraintEnd_toStartOf="@id/gift_left_baseline"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_anchor_avatar_request"
            android:layout_width="25dp"
            android:layout_height="25dp"
            android:scaleType="centerCrop"
            android:src="@drawable/placeholder"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_gift_request_text"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_gift_request_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="7dp"
            android:layout_marginEnd="4dp"
            android:ellipsize="marquee"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="start|center_vertical"
            android:marqueeRepeatLimit="marquee_forever"
            android:scrollHorizontally="true"
            android:singleLine="true"
            android:textColor="@android:color/white"
            android:textSize="13sp"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_gift_icon_request"
            app:layout_constraintStart_toEndOf="@id/iv_anchor_avatar_request"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="btn_send_gift_requestbtn_send_gift_requestbtn_send_gift_requestbtn_send_gift_request" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_gift_icon_request"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:scaleType="fitCenter"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/btn_gift_send_request"
            app:layout_constraintStart_toEndOf="@id/tv_gift_request_text"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/qinggua" />

        <com.score.callmetest.ui.widget.AlphaTextView
            android:id="@+id/btn_gift_send_request"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:background="@drawable/bg_ask_gift_send_shape"
            android:gravity="center"
            android:paddingHorizontal="15dp"
            android:text="@string/ask_gift_send"
            android:textColor="@android:color/white"
            android:textSize="13sp"
            android:textStyle="bold"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 送出礼物   -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ll_gift_send"
        android:layout_width="0dp"
        android:layout_height="36dp"
        android:layout_marginStart="15dp"
        android:layout_marginBottom="10dp"
        android:background="@drawable/bg_send_gift_shape"
        android:paddingHorizontal="10dp"
        android:visibility="gone"
        app:layout_constraintBottom_toTopOf="@id/ll_gift_request"
        app:layout_constraintEnd_toStartOf="@id/gift_left_baseline"
        app:layout_constraintStart_toStartOf="parent"
        tools:visibility="visible">


        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_gift_send_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="7dp"
            android:layout_marginEnd="4dp"
            android:ellipsize="marquee"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:gravity="start|center_vertical"
            android:marqueeRepeatLimit="marquee_forever"
            android:scrollHorizontally="true"
            android:singleLine="true"
            android:textColor="@android:color/white"
            android:textSize="13sp"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_gift_send"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Pinki Roy Julia: I sent Flowsers        x1" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/iv_gift_send"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:scaleType="fitCenter"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/tv_gift_send_num"
            app:layout_constraintStart_toEndOf="@id/tv_gift_send_text"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@drawable/qinggua" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_gift_send_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:textColor="#FFFC22"
            android:textSize="13sp"
            app:layout_constrainedWidth="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/iv_gift_send"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="x1" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 礼物展示背景动画容器 -->
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/gift_animation_bg_image"
        android:layout_width="318dp"
        android:layout_height="318dp"
        android:layout_gravity="center"
        android:scaleType="fitCenter"
        android:src="@drawable/gift_light_bg"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

    <!-- 礼物展示动画容器 -->
    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/gift_animation_image"
        android:layout_width="240dp"
        android:layout_height="240dp"
        android:layout_gravity="center"
        android:scaleType="fitCenter"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:visibility="visible" />

</androidx.constraintlayout.widget.ConstraintLayout>